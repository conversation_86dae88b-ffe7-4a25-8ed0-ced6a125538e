<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="org.qtproject.example.test"
    android:installLocation="auto"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="28" />

    <!--
         The following comment will be replaced upon deployment with default permissions based on the dependencies of the application.
         Remove the comment if you do not require these default permissions.
    -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <!--
         The following comment will be replaced upon deployment with default features based on the dependencies of the application.
         Remove the comment if you do not require these default features.
    -->

    <supports-screens
        android:anyDensity="true"
        android:largeScreens="true"
        android:normalScreens="true"
        android:smallScreens="true" />

    <application
        android:name="org.qtproject.qt5.android.bindings.QtApplication"
        android:debuggable="true"
        android:hardwareAccelerated="true"
        android:label="test" >
        <activity
            android:name="org.qtproject.qt5.android.bindings.QtActivity"
            android:configChanges="orientation|uiMode|screenLayout|screenSize|smallestScreenSize|layoutDirection|locale|fontScale|keyboard|keyboardHidden|navigation|mcc|mnc|density"
            android:label="test"
            android:launchMode="singleTop"
            android:screenOrientation="unspecified" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- Application arguments -->
            <!-- meta-data android:name="android.app.arguments" android:value="arg1 arg2 arg3"/ -->
            <!-- Application arguments -->

            <meta-data
                android:name="android.app.lib_name"
                android:value="test" />
            <meta-data
                android:name="android.app.qt_sources_resource_id"
                android:resource="@array/qt_sources" />
            <meta-data
                android:name="android.app.repository"
                android:value="default" />
            <meta-data
                android:name="android.app.qt_libs_resource_id"
                android:resource="@array/qt_libs" />
            <meta-data
                android:name="android.app.bundled_libs_resource_id"
                android:resource="@array/bundled_libs" />
            <!-- Deploy Qt libs as part of package -->
            <meta-data
                android:name="android.app.bundle_local_qt_libs"
                android:value="1" />
            <meta-data
                android:name="android.app.bundled_in_lib_resource_id"
                android:resource="@array/bundled_in_lib" />
            <meta-data
                android:name="android.app.bundled_in_assets_resource_id"
                android:resource="@array/bundled_in_assets" />
            <!-- Run with local libs -->
            <meta-data
                android:name="android.app.use_local_qt_libs"
                android:value="1" />
            <meta-data
                android:name="android.app.libs_prefix"
                android:value="/data/local/tmp/qt/" />
            <meta-data
                android:name="android.app.load_local_libs"
                android:value="plugins/platforms/android/libqtforandroid.so" />
            <meta-data
                android:name="android.app.load_local_jars"
                android:value="jar/QtAndroid.jar" />
            <meta-data
                android:name="android.app.static_init_classes"
                android:value="" />
            <!-- Used to specify custom system library path to run with local system libs -->
            <!-- <meta-data android:name="android.app.system_libs_prefix" android:value="/system/lib/"/> -->
            <!-- Messages maps -->
            <meta-data
                android:name="android.app.ministro_not_found_msg"
                android:value="@string/ministro_not_found_msg" />
            <meta-data
                android:name="android.app.ministro_needed_msg"
                android:value="@string/ministro_needed_msg" />
            <meta-data
                android:name="android.app.fatal_error_msg"
                android:value="@string/fatal_error_msg" />
            <meta-data
                android:name="android.app.unsupported_android_version"
                android:value="@string/unsupported_android_version" />
            <!-- Messages maps -->


            <!-- Splash screen -->
            <!--
                 Orientation-specific (portrait/landscape) data is checked first. If not available for current orientation,
                 then android.app.splash_screen_drawable. For best results, use together with splash_screen_sticky and
                 use hideSplashScreen() with a fade-out animation from Qt Android Extras to hide the splash screen when you
                 are done populating your window with content.
            -->
            <!-- meta-data android:name="android.app.splash_screen_drawable_portrait" android:resource="@drawable/logo_portrait" / -->
            <!-- meta-data android:name="android.app.splash_screen_drawable_landscape" android:resource="@drawable/logo_landscape" / -->
            <!-- meta-data android:name="android.app.splash_screen_drawable" android:resource="@drawable/logo"/ -->
            <!-- meta-data android:name="android.app.splash_screen_sticky" android:value="true"/ -->
            <!-- Splash screen -->


            <!-- Background running -->
            <!--
                 Warning: changing this value to true may cause unexpected crashes if the
                          application still try to draw after
                          "applicationStateChanged(Qt::ApplicationSuspended)"
                          signal is sent!
            -->
            <meta-data
                android:name="android.app.background_running"
                android:value="false" />
            <!-- Background running -->


            <!-- auto screen scale factor -->
            <meta-data
                android:name="android.app.auto_screen_scale_factor"
                android:value="false" />
            <!-- auto screen scale factor -->


            <!-- extract android style -->
            <!--
                 available android:values :
                * default - In most cases this will be the same as "full", but it can also be something else if needed, e.g., for compatibility reasons
                * full - useful QWidget & Quick Controls 1 apps
                * minimal - useful for Quick Controls 2 apps, it is much faster than "full"
                * none - useful for apps that don't use any of the above Qt modules
            -->
            <meta-data
                android:name="android.app.extract_android_style"
                android:value="default" />
            <!-- extract android style -->
        </activity>

        <!-- For adding service(s) please check: https://wiki.qt.io/AndroidServices -->

    </application>

</manifest>