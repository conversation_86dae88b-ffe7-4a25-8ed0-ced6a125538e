<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res"/><source path="F:\code\M2\test\test\Debug\android-build\res"/><source path="F:\code\M2\test\test\Debug\android-build\build\generated\res\rs\debug"/><source path="F:\code\M2\test\test\Debug\android-build\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res"><file name="splash" path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\layout\splash.xml" qualifiers="" type="layout"/><file path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\values\strings.xml" qualifiers=""><string name="ministro_not_found_msg">Can\'t find Ministro service.\nThe application can\'t start.</string><string name="ministro_needed_msg">This application requires Ministro service. Would you like to install it?</string><string name="fatal_error_msg">Your application encountered a fatal error and cannot continue.</string><string name="unsupported_android_version">This version of Android is not supported.</string></file><file path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\values-de\strings.xml" qualifiers="de"><string name="ministro_not_found_msg">Ministro-Dienst wurde nicht gefunden.\nAnwendung kann nicht gestartet werden</string><string name="ministro_needed_msg">Diese Anwendung benötigt den Ministro-Dienst. Möchten Sie ihn installieren?</string><string name="fatal_error_msg">In Ihrer Anwendung ist ein schwerwiegender Fehler aufgetreten, sie kann nicht fortgesetzt werden</string><string name="unsupported_android_version">Diese Android-Version wird nicht unterstützt.</string></file><file path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\values-el\strings.xml" qualifiers="el"><string name="ministro_not_found_msg">Δεν ήταν δυνατή η εύρεση της υπηρεσίας Ministro. Δεν είναι δυνατή η εκκίνηση της εφαρμογής.</string><string name="ministro_needed_msg">Η εφαρμογή απαιτεί την υπηρεσία Ministro. Να εγκατασταθεί η υπηρεσία?</string><string name="fatal_error_msg">Παρουσιάστηκε ένα κρίσιμο σφάλμα και η εφαρμογή δεν μπορεί να συνεχίσει.</string><string name="unsupported_android_version">Αυτή η έκδοση του Android δεν υποστηρίζεται.</string></file><file path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\values-es\strings.xml" qualifiers="es"><string name="ministro_not_found_msg">Servicio Ministro inesistente. Imposible ejecutar la aplicación.</string><string name="ministro_needed_msg">Esta aplicación requiere el servicio Ministro. Instalarlo?</string><string name="fatal_error_msg">La aplicación ha causado un error grave y no es posible continuar.</string><string name="unsupported_android_version">Esta versión de Android no es compatible.</string></file><file path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\values-et\strings.xml" qualifiers="et"><string name="ministro_not_found_msg">Ei suuda leida Ministro teenust.\nProgrammi ei saa käivitada.</string><string name="ministro_needed_msg">See programm vajab Ministro teenust.\nKas soovite paigaldada?</string><string name="fatal_error_msg">Programmiga juhtus fataalne viga.\nKahjuks ei saa jätkata.</string><string name="unsupported_android_version">Seda Androidi versiooni ei toetata.</string></file><file path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\values-fa\strings.xml" qualifiers="fa"><string name="ministro_not_found_msg">سرویس Ministro را پیدا نمی‌کند. برنامه نمی‌تواند آغاز شود.</string><string name="ministro_needed_msg">این نرم‌افزار به سرویس Ministro احتیاج دارد. آیا دوست دارید آن را نصب کنید؟</string><string name="fatal_error_msg">خطایی اساسی در برنامه‌تان رخ داد و اجرای برنامه نمی‌تواند ادامه یابد.</string><string name="unsupported_android_version">این نسخه از Android پشتیبانی نمی شود</string></file><file path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\values-fr\strings.xml" qualifiers="fr"><string name="ministro_not_found_msg">Le service Ministro est introuvable.\nL\'application ne peut pas démarrer.</string><string name="ministro_needed_msg">Cette application requiert le service Ministro. Voulez-vous l\'installer?</string><string name="fatal_error_msg">Votre application a rencontré une erreur fatale et ne peut pas continuer.</string><string name="unsupported_android_version">Cette version d\'Android n\'est pas supportée.</string></file><file path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\values-in\strings.xml" qualifiers="in"><string name="ministro_not_found_msg">Layanan Ministro tidak bisa ditemukan.\nAplikasi tidak bisa dimulai.</string><string name="ministro_needed_msg">Aplikasi ini membutuhkan layanan Ministro. Apakah Anda ingin menginstalnya?</string><string name="fatal_error_msg">Aplikasi Anda mengalami kesalahan fatal dan tidak dapat melanjutkan.</string><string name="unsupported_android_version">Versi Android ini tidak didukung.</string></file><file path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\values-it\strings.xml" qualifiers="it"><string name="ministro_not_found_msg">Servizio Ministro inesistente. Impossibile eseguire \nl\'applicazione.</string><string name="ministro_needed_msg">Questa applicazione richiede il servizio Ministro.Installarlo?</string><string name="fatal_error_msg">L\'applicazione ha provocato un errore grave e non puo\' continuare.</string><string name="unsupported_android_version">Questa versione di Android non è supportata.</string></file><file path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\values-ja\strings.xml" qualifiers="ja"><string name="ministro_not_found_msg">Ministroサービスが見つかりません。\nアプリケーションが起動できません。</string><string name="ministro_needed_msg">このアプリケーションにはMinistroサービスが必要です。 インストールしてもよろしいですか？</string><string name="fatal_error_msg">アプリケーションで致命的なエラーが発生したため続行できません。</string><string name="unsupported_android_version">このバージョンのAndroidはサポートされていません。</string></file><file path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\values-ms\strings.xml" qualifiers="ms"><string name="ministro_not_found_msg">Tidak jumpa servis Ministro.\nAplikasi tidak boleh dimulakan.</string><string name="ministro_needed_msg">Aplikasi ini memerlukan servis Ministro. Adakah anda ingin pasang servis itu?</string><string name="fatal_error_msg">Aplikasi anda menemui ralat muat dan tidak boleh diteruskan.</string><string name="unsupported_android_version">Versi Android ini tidak disokong.</string></file><file path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\values-nb\strings.xml" qualifiers="nb"><string name="ministro_not_found_msg">Kan ikke finne tjenesten Ministro. Applikasjonen kan ikke starte.</string><string name="ministro_needed_msg">Denne applikasjonen krever tjenesten Ministro. Vil du installere denne?</string><string name="fatal_error_msg">Applikasjonen fikk en kritisk feil og kan ikke fortsette</string><string name="unsupported_android_version">Denne versjonen av Android støttes ikke.</string></file><file path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\values-nl\strings.xml" qualifiers="nl"><string name="ministro_not_found_msg">De Ministro service is niet gevonden.\nDe applicatie kan niet starten.</string><string name="ministro_needed_msg">Deze applicatie maakt gebruik van de Ministro service. Wilt u deze installeren?</string><string name="fatal_error_msg">Er is een fatale fout in de applicatie opgetreden. De applicatie kan niet verder gaan.</string><string name="unsupported_android_version">Deze versie van Android wordt niet ondersteund.</string></file><file path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\values-pl\strings.xml" qualifiers="pl"><string name="ministro_not_found_msg">Usługa Ministro nie została znaleziona.\nAplikacja nie może zostać uruchomiona.</string><string name="ministro_needed_msg">Aplikacja wymaga usługi Ministro. Czy chcesz ją zainstalować?</string><string name="fatal_error_msg">Wystąpił błąd krytyczny. Aplikacja zostanie zamknięta.</string><string name="unsupported_android_version">Ta wersja Androida nie jest obsługiwana.</string></file><file path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\values-pt-rBR\strings.xml" qualifiers="pt-rBR"><string name="ministro_not_found_msg">Não foi possível encontrar o serviço Ministro.\nA aplicação não pode iniciar.</string><string name="ministro_needed_msg">Essa aplicação requer o serviço Ministro. Gostaria de instalá-lo?</string><string name="fatal_error_msg">Sua aplicação encontrou um erro fatal e não pode continuar.</string><string name="unsupported_android_version">Esta versão do Android não é suportada.</string></file><file path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\values-ro\strings.xml" qualifiers="ro"><string name="ministro_not_found_msg">Serviciul Ministro nu poate fi găsit.\nAplicaţia nu poate porni.</string><string name="ministro_needed_msg">Această aplicaţie necesită serviciul Ministro.\nDoriţi să-l instalaţi?</string><string name="fatal_error_msg">Aplicaţia dumneavoastră a întâmpinat o eroare fatală şi nu poate continua.</string><string name="unsupported_android_version">Această versiune de Android nu este suportată.</string></file><file path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\values-rs\strings.xml" qualifiers="rs"><string name="ministro_not_found_msg">Ministro servise nije pronađen. Aplikacija ne može biti pokrenuta.</string><string name="ministro_needed_msg">Ova aplikacija zahteva Ministro servis. Želite li da ga instalirate?</string><string name="fatal_error_msg">Vaša aplikacija je naišla na fatalnu grešku i ne može nastaviti sa radom.</string><string name="unsupported_android_version">Ova verzija Android-a nije podržana.</string></file><file path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\values-ru\strings.xml" qualifiers="ru"><string name="ministro_not_found_msg">Сервис Ministro не найден.\nПриложение нельзя запустить.</string><string name="ministro_needed_msg">Этому приложению необходим сервис Ministro. Вы хотите его установить?</string><string name="fatal_error_msg">Ваше приложение столкнулось с фатальной ошибкой и не может более работать.</string><string name="unsupported_android_version">Эта версия Android не поддерживается.</string></file><file path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\values-zh-rCN\strings.xml" qualifiers="zh-rCN"><string name="ministro_not_found_msg">无法找到Ministro服务。\n应用程序无法启动。</string><string name="ministro_needed_msg">此应用程序需要Ministro服务。您想安装它吗？</string><string name="fatal_error_msg">您的应用程序遇到一个致命错误导致它无法继续。</string><string name="unsupported_android_version">这个版本的安卓系统不被支持。</string></file><file path="G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\src\android\java\res\values-zh-rTW\strings.xml" qualifiers="zh-rTW"><string name="ministro_not_found_msg">無法找到Ministro服務。\n應用程序無法啟動。</string><string name="ministro_needed_msg">此應用程序需要Ministro服務。您想安裝它嗎？</string><string name="fatal_error_msg">您的應用程序遇到一個致命錯誤導致它無法繼續。</string><string name="unsupported_android_version">這個版本的安卓系統不被支持。</string></file></source><source path="F:\code\M2\test\test\Debug\android-build\res"><file path="F:\code\M2\test\test\Debug\android-build\res\values\libs.xml" qualifiers=""><array name="qt_sources">
        <item>https://download.qt.io/ministro/android/qt5/qt-5.9</item>
    </array><array name="bundled_libs">
        
    </array><array name="qt_libs">
         <item>c++_shared</item>
<item>Qt5Core</item>
<item>Qt5Gui</item>
<item>Qt5Widgets</item>

     </array><array name="bundled_in_lib">
        <item>libplugins_platforms_android_libqtforandroid.so:plugins/platforms/android/libqtforandroid.so</item>
<item>libplugins_imageformats_libqgif.so:plugins/imageformats/libqgif.so</item>
<item>libplugins_imageformats_libqicns.so:plugins/imageformats/libqicns.so</item>
<item>libplugins_imageformats_libqico.so:plugins/imageformats/libqico.so</item>
<item>libplugins_imageformats_libqjpeg.so:plugins/imageformats/libqjpeg.so</item>
<item>libplugins_imageformats_libqtga.so:plugins/imageformats/libqtga.so</item>
<item>libplugins_imageformats_libqtiff.so:plugins/imageformats/libqtiff.so</item>
<item>libplugins_imageformats_libqwbmp.so:plugins/imageformats/libqwbmp.so</item>
<item>libplugins_imageformats_libqwebp.so:plugins/imageformats/libqwebp.so</item>
<item>libplugins_styles_libqandroidstyle.so:plugins/styles/libqandroidstyle.so</item>

    </array><array name="bundled_in_assets">
        
    </array></file></source><source path="F:\code\M2\test\test\Debug\android-build\build\generated\res\rs\debug"/><source path="F:\code\M2\test\test\Debug\android-build\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\code\M2\test\test\Debug\android-build\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\code\M2\test\test\Debug\android-build\src\debug\res"/></dataSet><mergedItems/></merger>