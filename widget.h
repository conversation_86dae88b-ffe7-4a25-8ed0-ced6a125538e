#ifndef WIDGET_H
#define WIDGET_H

#include <QWidget>
#include <QListWidget>
#include <QLabel>
#include <QMovie>
#include <QTimer>
#include <QScrollBar>
#include <QTouchEvent>
#include <QGestureEvent>
#include <QPanGesture>
#include <QVBoxLayout>

QT_BEGIN_NAMESPACE
namespace Ui { class Widget; }
QT_END_NAMESPACE

class Widget : public QWidget
{
    Q_OBJECT

public:
    Widget(QWidget *parent = nullptr);
    ~Widget();

private slots:
    void onRefresh();
    void onLoadMore();
    void simulateDataRefresh();
    void simulateDataLoad();

private:
    void setupUI();
    void addSampleData(int count, int startIndex = 0);

    Ui::Widget *ui;
    PullRefreshListWidget *m_listWidget;
    QTimer *m_dataTimer;
    int m_dataIndex;
};
#endif // WIDGET_H
