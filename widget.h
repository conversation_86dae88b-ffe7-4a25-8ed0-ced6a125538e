#ifndef WIDGET_H
#define WIDGET_H

#include <QWidget>
#include "pulltorefreshlistwidget.h"

QT_BEGIN_NAMESPACE
namespace Ui { class Widget; }
QT_END_NAMESPACE

// 示例：继承PullToRefreshListWidget并实现刷新逻辑
class MyRefreshListWidget : public PullToRefreshListWidget
{
    Q_OBJECT

public:
    explicit MyRefreshListWidget(QWidget *parent = nullptr);

protected:
    void onRefreshRequested() override;

private slots:
    void simulateDataLoading();

private:
    int m_itemCount;
};

class Widget : public QWidget
{
    Q_OBJECT

public:
    Widget(QWidget *parent = nullptr);
    ~Widget();

private:
    Ui::Widget *ui;
    MyRefreshListWidget *m_listWidget;
};
#endif // WIDGET_H
