<!DOCTYPE html>
<html>
<head>
    <title>上滑加载更多 - Loading图标生成器</title>
    <style>
        .loading-spinner {
            width: 24px;
            height: 24px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .instructions {
            max-width: 600px;
            margin: 20px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>上滑加载更多 - Loading图标生成器</h1>
        
        <div class="loading-spinner"></div>
        
        <div class="instructions">
            <h3>如何获取loading.gif文件：</h3>
            
            <h4>方法1：在线生成器</h4>
            <ul>
                <li><a href="https://loading.io/" target="_blank">Loading.io</a> - 免费的加载动画生成器</li>
                <li><a href="https://icons8.com/preloaders/" target="_blank">Icons8 Preloaders</a> - 各种预设的加载动画</li>
                <li><a href="https://cssload.net/" target="_blank">CSSLoad.net</a> - CSS和GIF加载动画</li>
            </ul>
            
            <h4>方法2：使用现成的GIF</h4>
            <p>你可以从以下网站下载现成的loading.gif文件：</p>
            <ul>
                <li>搜索 "loading spinner gif 24x24"</li>
                <li>确保文件大小为24x24像素</li>
                <li>背景透明</li>
                <li>文件名必须为 "loading.gif"</li>
            </ul>
            
            <h4>方法3：录制屏幕</h4>
            <p>你可以录制上面的CSS动画并转换为GIF：</p>
            <ol>
                <li>使用屏幕录制工具录制上面的旋转动画</li>
                <li>使用在线工具将视频转换为GIF</li>
                <li>调整大小为24x24像素</li>
                <li>保存为loading.gif</li>
            </ol>
            
            <h4>文件位置</h4>
            <p>将loading.gif文件放在项目的 <code>icons/</code> 目录下。</p>
            
            <h4>备注</h4>
            <p>如果没有loading.gif文件，程序会使用CSS样式的旋转动画作为备选方案。</p>
            
            <h4>功能说明</h4>
            <p>这个组件实现了<strong>上滑到底部加载更多</strong>的功能：</p>
            <ul>
                <li>当用户滑动到列表底部时</li>
                <li>继续向上拖拽会显示加载指示器</li>
                <li>释放后触发加载更多数据的逻辑</li>
                <li>适合实现分页加载、无限滚动等功能</li>
            </ul>
        </div>
    </div>
</body>
</html>
