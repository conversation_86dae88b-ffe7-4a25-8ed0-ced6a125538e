#Internal package file, do not edit.
#Fri Jun 20 11:54:20 CST 2025
17.baseType=DIRECTORY
4.path=lib/arm64-v8a/libplugins_imageformats_libqicns.so
8.set=NATIVE_RESOURCE
19.baseType=DIRECTORY
11.set=NATIVE_RESOURCE
2.baseType=DIRECTORY
4.baseType=DIRECTORY
14.base=F\:\\code\\M2\\test\\test\\Debug\\android-build\\build\\intermediates\\transforms\\mergeJniLibs\\debug\\0
16.set=ASSET
count=20
6.base=F\:\\code\\M2\\test\\test\\Debug\\android-build\\build\\intermediates\\transforms\\mergeJniLibs\\debug\\0
13.path=lib/arm64-v8a/libplugins_imageformats_libqgif.so
6.baseType=DIRECTORY
2.set=NATIVE_RESOURCE
5.path=lib/arm64-v8a/libQt5Core.so
8.baseType=DIRECTORY
0.base=F\:\\code\\M2\\test\\test\\Debug\\android-build\\build\\intermediates\\transforms\\mergeJniLibs\\debug\\0
7.set=NATIVE_RESOURCE
10.set=ASSET
10.baseType=DIRECTORY
15.base=F\:\\code\\M2\\test\\test\\Debug\\android-build\\build\\intermediates\\transforms\\mergeJniLibs\\debug\\0
7.base=F\:\\code\\M2\\test\\test\\Debug\\android-build\\build\\intermediates\\transforms\\mergeJniLibs\\debug\\0
14.path=lib/arm64-v8a/libplugins_styles_libqandroidstyle.so
15.set=NATIVE_RESOURCE
6.path=lib/arm64-v8a/libtest.so
1.base=F\:\\code\\M2\\test\\test\\Debug\\android-build\\build\\intermediates\\transforms\\mergeJniLibs\\debug\\0
1.set=NATIVE_RESOURCE
0.path=lib/arm64-v8a/libplugins_imageformats_libqico.so
12.baseType=DIRECTORY
6.set=NATIVE_RESOURCE
16.base=F\:\\code\\M2\\test\\test\\Debug\\android-build\\build\\intermediates\\merged_assets\\debug\\mergeDebugAssets\\out
8.base=F\:\\code\\M2\\test\\test\\Debug\\android-build\\build\\intermediates\\transforms\\mergeJniLibs\\debug\\0
15.path=lib/arm64-v8a/libQt5Widgets.so
14.baseType=DIRECTORY
17.base=F\:\\code\\M2\\test\\test\\Debug\\android-build\\build\\intermediates\\transforms\\mergeJniLibs\\debug\\0
10.base=F\:\\code\\M2\\test\\test\\Debug\\android-build\\build\\intermediates\\merged_assets\\debug\\mergeDebugAssets\\out
7.path=lib/arm64-v8a/libplugins_imageformats_libqjpeg.so
16.baseType=DIRECTORY
14.set=NATIVE_RESOURCE
2.base=F\:\\code\\M2\\test\\test\\Debug\\android-build\\build\\intermediates\\transforms\\mergeJniLibs\\debug\\0
18.baseType=DIRECTORY
1.path=lib/arm64-v8a/libplugins_imageformats_libqtiff.so
19.set=NATIVE_RESOURCE
0.set=NATIVE_RESOURCE
1.baseType=DIRECTORY
3.baseType=DIRECTORY
5.set=NATIVE_RESOURCE
9.base=F\:\\code\\M2\\test\\test\\Debug\\android-build\\build\\intermediates\\transforms\\dexMerger\\debug\\0
16.path=--Added-by-androiddeployqt--/qt_cache_pregenerated_file_list
18.base=F\:\\code\\M2\\test\\test\\Debug\\android-build\\build\\intermediates\\transforms\\mergeJniLibs\\debug\\0
5.baseType=DIRECTORY
11.base=F\:\\code\\M2\\test\\test\\Debug\\android-build\\build\\intermediates\\transforms\\mergeJniLibs\\debug\\0
8.path=lib/arm64-v8a/libc++_shared.so
17.path=lib/arm64-v8a/libplugins_imageformats_libqtga.so
7.baseType=DIRECTORY
3.base=F\:\\code\\M2\\test\\test\\Debug\\android-build\\build\\intermediates\\transforms\\mergeJniLibs\\debug\\0
10.path=--Added-by-androiddeployqt--/debugger.command
13.set=NATIVE_RESOURCE
2.path=lib/arm64-v8a/libplugins_imageformats_libqwbmp.so
9.baseType=DIRECTORY
18.set=NATIVE_RESOURCE
19.base=F\:\\code\\M2\\test\\test\\Debug\\android-build\\build\\intermediates\\transforms\\mergeJniLibs\\debug\\0
12.base=F\:\\code\\M2\\test\\test\\Debug\\android-build\\build\\intermediates\\transforms\\mergeJniLibs\\debug\\0
4.set=NATIVE_RESOURCE
9.path=classes.dex
18.path=lib/arm64-v8a/libplugins_imageformats_libqwebp.so
4.base=F\:\\code\\M2\\test\\test\\Debug\\android-build\\build\\intermediates\\transforms\\mergeJniLibs\\debug\\0
11.path=lib/arm64-v8a/libQt5Gui.so
9.set=DEX
3.path=lib/arm64-v8a/libgdbserver.so
12.set=NATIVE_RESOURCE
11.baseType=DIRECTORY
0.baseType=DIRECTORY
17.set=NATIVE_RESOURCE
13.baseType=DIRECTORY
13.base=F\:\\code\\M2\\test\\test\\Debug\\android-build\\build\\intermediates\\transforms\\mergeJniLibs\\debug\\0
19.path=lib/arm64-v8a/gdbserver
15.baseType=DIRECTORY
3.set=NATIVE_RESOURCE
5.base=F\:\\code\\M2\\test\\test\\Debug\\android-build\\build\\intermediates\\transforms\\mergeJniLibs\\debug\\0
12.path=lib/arm64-v8a/libplugins_platforms_android_libqtforandroid.so
