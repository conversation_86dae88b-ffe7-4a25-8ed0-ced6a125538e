<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\code\M2\test\test\Debug\android-build\libs"><file name="arm64-v8a/gdbserver" path="F:\code\M2\test\test\Debug\android-build\libs\arm64-v8a\gdbserver"/><file name="arm64-v8a/libc++_shared.so" path="F:\code\M2\test\test\Debug\android-build\libs\arm64-v8a\libc++_shared.so"/><file name="arm64-v8a/libgdbserver.so" path="F:\code\M2\test\test\Debug\android-build\libs\arm64-v8a\libgdbserver.so"/><file name="arm64-v8a/libplugins_imageformats_libqgif.so" path="F:\code\M2\test\test\Debug\android-build\libs\arm64-v8a\libplugins_imageformats_libqgif.so"/><file name="arm64-v8a/libplugins_imageformats_libqicns.so" path="F:\code\M2\test\test\Debug\android-build\libs\arm64-v8a\libplugins_imageformats_libqicns.so"/><file name="arm64-v8a/libplugins_imageformats_libqico.so" path="F:\code\M2\test\test\Debug\android-build\libs\arm64-v8a\libplugins_imageformats_libqico.so"/><file name="arm64-v8a/libplugins_imageformats_libqjpeg.so" path="F:\code\M2\test\test\Debug\android-build\libs\arm64-v8a\libplugins_imageformats_libqjpeg.so"/><file name="arm64-v8a/libplugins_imageformats_libqtga.so" path="F:\code\M2\test\test\Debug\android-build\libs\arm64-v8a\libplugins_imageformats_libqtga.so"/><file name="arm64-v8a/libplugins_imageformats_libqtiff.so" path="F:\code\M2\test\test\Debug\android-build\libs\arm64-v8a\libplugins_imageformats_libqtiff.so"/><file name="arm64-v8a/libplugins_imageformats_libqwbmp.so" path="F:\code\M2\test\test\Debug\android-build\libs\arm64-v8a\libplugins_imageformats_libqwbmp.so"/><file name="arm64-v8a/libplugins_imageformats_libqwebp.so" path="F:\code\M2\test\test\Debug\android-build\libs\arm64-v8a\libplugins_imageformats_libqwebp.so"/><file name="arm64-v8a/libplugins_platforms_android_libqtforandroid.so" path="F:\code\M2\test\test\Debug\android-build\libs\arm64-v8a\libplugins_platforms_android_libqtforandroid.so"/><file name="arm64-v8a/libplugins_styles_libqandroidstyle.so" path="F:\code\M2\test\test\Debug\android-build\libs\arm64-v8a\libplugins_styles_libqandroidstyle.so"/><file name="arm64-v8a/libQt5Core.so" path="F:\code\M2\test\test\Debug\android-build\libs\arm64-v8a\libQt5Core.so"/><file name="arm64-v8a/libQt5Gui.so" path="F:\code\M2\test\test\Debug\android-build\libs\arm64-v8a\libQt5Gui.so"/><file name="arm64-v8a/libQt5Widgets.so" path="F:\code\M2\test\test\Debug\android-build\libs\arm64-v8a\libQt5Widgets.so"/><file name="arm64-v8a/libtest.so" path="F:\code\M2\test\test\Debug\android-build\libs\arm64-v8a\libtest.so"/><file name="QtAndroid.jar" path="F:\code\M2\test\test\Debug\android-build\libs\QtAndroid.jar"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\code\M2\test\test\Debug\android-build\src\debug\jniLibs"/></dataSet></merger>