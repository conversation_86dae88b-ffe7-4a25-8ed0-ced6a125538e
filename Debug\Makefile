#############################################################################
# Makefile for building: libtest.so
# Generated by qmake (3.1) (Qt 5.13.2)
# Project:  ..\test.pro
# Template: app
# Command: G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\bin\qmake.exe -o Makefile ..\test.pro -spec android-clang "CONFIG+=debug" "CONFIG+=qml_debug"
#############################################################################

MAKEFILE      = Makefile

EQ            = =

####### Compiler, tools and options

CC            = G:\android\android-ndk-r20b/toolchains/llvm/prebuilt/windows-x86_64/bin/clang
CXX           = G:\android\android-ndk-r20b/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++
DEFINES       = -DQT_DEPRECATED_WARNINGS -DQT_QML_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB
CFLAGS        = -D__ANDROID_API__=21 -target aarch64-none-linux-android -gcc-toolchain G:\android\android-ndk-r20b/toolchains/aarch64-linux-android-4.9/prebuilt/windows-x86_64 -fno-limit-debug-info -DANDROID_HAS_WSTRING --sysroot=G:\android\android-ndk-r20b/sysroot -isystem G:\android\android-ndk-r20b/sysroot/usr/include/aarch64-linux-android -isystem G:\android\android-ndk-r20b/sources/cxx-stl/llvm-libc++/include -isystem G:\android\android-ndk-r20b/sources/android/support/include -isystem G:\android\android-ndk-r20b/sources/cxx-stl/llvm-libc++abi/include -fstack-protector-strong -DANDROID -g -Wall -W -D_REENTRANT -fPIC $(DEFINES)
CXXFLAGS      = -D__ANDROID_API__=21 -target aarch64-none-linux-android -gcc-toolchain G:\android\android-ndk-r20b/toolchains/aarch64-linux-android-4.9/prebuilt/windows-x86_64 -fno-limit-debug-info -DANDROID_HAS_WSTRING --sysroot=G:\android\android-ndk-r20b/sysroot -isystem G:\android\android-ndk-r20b/sysroot/usr/include/aarch64-linux-android -isystem G:\android\android-ndk-r20b/sources/cxx-stl/llvm-libc++/include -isystem G:\android\android-ndk-r20b/sources/android/support/include -isystem G:\android\android-ndk-r20b/sources/cxx-stl/llvm-libc++abi/include -fstack-protector-strong -DANDROID -g -g -std=gnu++11 -Wall -W -D_REENTRANT -fPIC $(DEFINES)
INCPATH       = -I..\..\test -I. -IG:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include -IG:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets -IG:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui -IG:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore -I. -I. -IG:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\android-clang
QMAKE         = G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = cmd /c move
TAR           = 
COMPRESS      = 
DISTNAME      = libtest.so1.0.0
DISTDIR = F:\code\M2\test\test\Debug\.tmp\libtest.so1.0.0
LINK          = G:\android\android-ndk-r20b/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++ -D__ANDROID_API__=21 -target aarch64-none-linux-android -gcc-toolchain G:\android\android-ndk-r20b/toolchains/aarch64-linux-android-4.9/prebuilt/windows-x86_64 -fno-limit-debug-info -Wl,--exclude-libs,libgcc.a -Wl,--exclude-libs,libatomic.a -nostdlib++
LFLAGS        = --sysroot=G:\android\android-ndk-r20b/platforms/android-21/arch-arm64/ -Wl,-soname,libtest.so -Wl,-rpath=G:/Qt/Qt5.13.2/5.13.2/android_arm64_v8a/lib -Wl,--no-undefined -Wl,-z,noexecstack -shared
LIBS          = $(SUBLIBS) G:/Qt/Qt5.13.2/5.13.2/android_arm64_v8a/lib/libQt5Widgets.so -Lc:\Utils\Android\android-ndk-r19c/sources/cxx-stl/llvm-libc++/libs/arm64-v8a G:/Qt/Qt5.13.2/5.13.2/android_arm64_v8a/lib/libQt5Gui.so G:/Qt/Qt5.13.2/5.13.2/android_arm64_v8a/lib/libQt5Core.so -lGLESv2  -LG:\android\android-ndk-r20b/sources/cxx-stl/llvm-libc++/libs/arm64-v8a G:\android\android-ndk-r20b/sources/cxx-stl/llvm-libc++/libs/arm64-v8a/libc++.so.21 -llog -lz -lm -ldl -lc
AR            = G:\android\android-ndk-r20b/toolchains/aarch64-linux-android-4.9/prebuilt/windows-x86_64/bin/aarch64-linux-android-ar cqs
RANLIB        = G:\android\android-ndk-r20b/toolchains/aarch64-linux-android-4.9/prebuilt/windows-x86_64/bin/aarch64-linux-android-ranlib
SED           = $(QMAKE) -install sed
STRIP         = 

####### Output directory

OBJECTS_DIR   = ./

####### Files

SOURCES       = ..\main.cpp \
		..\widget.cpp \
		..\pulltorefreshlistwidget.cpp qrc_resources.cpp \
		moc_widget.cpp \
		moc_pulltorefreshlistwidget.cpp
OBJECTS       = main.obj \
		widget.obj \
		pulltorefreshlistwidget.obj \
		qrc_resources.obj \
		moc_widget.obj \
		moc_pulltorefreshlistwidget.obj
DIST          = ..\android\AndroidManifest.xml \
		..\android\build.gradle \
		..\android\gradle\wrapper\gradle-wrapper.jar \
		..\android\gradle\wrapper\gradle-wrapper.properties \
		..\android\gradlew \
		..\android\gradlew.bat \
		..\android\res\values\libs.xml ..\widget.h \
		..\pulltorefreshlistwidget.h ..\main.cpp \
		..\widget.cpp \
		..\pulltorefreshlistwidget.cpp
QMAKE_TARGET  = libtest.so
DESTDIR       = 
TARGET        = libtest.so


first: all
####### Build rules

libtest.so: ui_widget.h $(OBJECTS)  
	$(LINK) $(LFLAGS) -o $(TARGET) $(OBJECTS) $(OBJCOMP) $(LIBS)

Makefile: ..\test.pro G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\android-clang\qmake.conf G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\spec_pre.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\common\unix.conf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\common\linux.conf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\common\sanitize.conf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\common\gcc-base.conf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\common\gcc-base-unix.conf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\common\clang.conf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\qdevice.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\device_config.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\common\android-base-head.conf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\qconfig.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3danimation.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3danimation_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dcore.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dcore_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dextras.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dextras_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dinput.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dinput_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dlogic.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dlogic_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquick.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquick_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquickanimation.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquickanimation_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquickextras.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquickextras_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquickinput.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquickinput_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquickrender.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquickrender_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquickscene2d.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquickscene2d_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3drender.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3drender_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_accessibility_support_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_androidextras.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_androidextras_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_av.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_av_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_avwidgets.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_avwidgets_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_bluetooth.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_bluetooth_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_bodymovin_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_bootstrap_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_charts.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_charts_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_concurrent.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_concurrent_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_core.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_core_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_datavisualization.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_datavisualization_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_devicediscovery_support_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_edid_support_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_egl_support_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_eventdispatcher_support_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_fb_support_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_fontdatabase_support_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_gamepad.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_gamepad_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_gui.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_gui_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_help.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_help_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_input_support_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_location.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_location_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_multimedia.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_multimedia_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_multimediawidgets.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_multimediawidgets_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_network.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_network_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_networkauth.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_networkauth_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_nfc.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_nfc_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_opengl.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_opengl_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_openglextensions.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_openglextensions_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_packetprotocol_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_platformcompositor_support_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_positioning.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_positioning_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_positioningquick.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_positioningquick_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_printsupport.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_printsupport_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_purchasing.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_purchasing_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_qml.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_qml_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_qmldebug_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_qmldevtools_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_qmltest.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_qmltest_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_qtmultimediaquicktools_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_quick.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_quick_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_quickcontrols2.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_quickcontrols2_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_quickparticles_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_quickshapes_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_quicktemplates2.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_quicktemplates2_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_quickwidgets.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_quickwidgets_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_remoteobjects.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_remoteobjects_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_repparser.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_repparser_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_script.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_script_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_scripttools.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_scripttools_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_scxml.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_scxml_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_sensors.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_sensors_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_serialport.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_serialport_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_service_support_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_sql.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_sql_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_svg.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_svg_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_testlib.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_testlib_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_texttospeech.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_texttospeech_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_theme_support_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_uiplugin.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_uitools.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_uitools_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_vulkan_support_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_webchannel.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_webchannel_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_websockets.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_websockets_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_webview.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_webview_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_widgets.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_widgets_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_xml.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_xml_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_xmlpatterns.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_xmlpatterns_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_zlib_private.pri \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\qt_functions.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\qt_config.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\common\android-base-tail.conf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\android-clang\qmake.conf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\spec_post.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\android\spec_post.prf \
		.qmake.stash \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\exclusive_builds.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\toolchain.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\default_pre.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\resolve_config.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\default_post.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\qml_debug.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\android\android.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\android\android_deployment_settings.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\warn_on.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\qt.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\resources.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\moc.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\unix\opengl.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\uic.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\unix\thread.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\qmake_use.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\file_copies.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\testcase_targets.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\exceptions.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\yacc.prf \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\lex.prf \
		..\test.pro \
		..\resources.qrc \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\lib\libQt5Widgets.prl \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\lib\libQt5Gui.prl \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\lib\libQt5Core.prl
	$(QMAKE) -o Makefile ..\test.pro -spec android-clang "CONFIG+=debug" "CONFIG+=qml_debug"
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\spec_pre.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\common\unix.conf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\common\linux.conf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\common\sanitize.conf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\common\gcc-base.conf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\common\gcc-base-unix.conf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\common\clang.conf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\qdevice.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\device_config.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\common\android-base-head.conf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\qconfig.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3danimation.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3danimation_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dcore.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dcore_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dextras.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dextras_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dinput.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dinput_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dlogic.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dlogic_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquick.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquick_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquickanimation.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquickanimation_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquickextras.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquickextras_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquickinput.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquickinput_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquickrender.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquickrender_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquickscene2d.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3dquickscene2d_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3drender.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_3drender_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_accessibility_support_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_androidextras.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_androidextras_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_av.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_av_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_avwidgets.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_avwidgets_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_bluetooth.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_bluetooth_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_bodymovin_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_bootstrap_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_charts.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_charts_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_concurrent.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_concurrent_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_core.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_core_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_datavisualization.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_datavisualization_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_devicediscovery_support_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_edid_support_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_egl_support_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_eventdispatcher_support_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_fb_support_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_fontdatabase_support_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_gamepad.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_gamepad_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_gui.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_gui_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_help.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_help_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_input_support_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_location.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_location_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_multimedia.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_multimedia_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_multimediawidgets.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_multimediawidgets_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_network.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_network_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_networkauth.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_networkauth_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_nfc.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_nfc_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_opengl.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_opengl_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_openglextensions.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_openglextensions_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_packetprotocol_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_platformcompositor_support_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_positioning.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_positioning_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_positioningquick.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_positioningquick_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_printsupport.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_printsupport_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_purchasing.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_purchasing_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_qml.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_qml_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_qmldebug_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_qmldevtools_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_qmltest.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_qmltest_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_qtmultimediaquicktools_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_quick.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_quick_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_quickcontrols2.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_quickcontrols2_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_quickparticles_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_quickshapes_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_quicktemplates2.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_quicktemplates2_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_quickwidgets.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_quickwidgets_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_remoteobjects.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_remoteobjects_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_repparser.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_repparser_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_script.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_script_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_scripttools.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_scripttools_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_scxml.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_scxml_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_sensors.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_sensors_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_serialport.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_serialport_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_service_support_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_sql.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_sql_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_svg.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_svg_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_testlib.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_testlib_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_texttospeech.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_texttospeech_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_theme_support_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_uiplugin.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_uitools.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_uitools_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_vulkan_support_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_webchannel.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_webchannel_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_websockets.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_websockets_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_webview.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_webview_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_widgets.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_widgets_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_xml.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_xml_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_xmlpatterns.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_xmlpatterns_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\modules\qt_lib_zlib_private.pri:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\qt_functions.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\qt_config.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\common\android-base-tail.conf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\android-clang\qmake.conf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\spec_post.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\android\spec_post.prf:
.qmake.stash:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\exclusive_builds.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\toolchain.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\default_pre.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\resolve_config.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\default_post.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\qml_debug.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\android\android.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\android\android_deployment_settings.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\warn_on.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\qt.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\resources.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\moc.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\unix\opengl.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\uic.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\unix\thread.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\qmake_use.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\file_copies.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\testcase_targets.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\exceptions.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\yacc.prf:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\lex.prf:
..\test.pro:
..\resources.qrc:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\lib\libQt5Widgets.prl:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\lib\libQt5Gui.prl:
G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\lib\libQt5Core.prl:
qmake: FORCE
	@$(QMAKE) -o Makefile ..\test.pro -spec android-clang "CONFIG+=debug" "CONFIG+=qml_debug"

qmake_all: FORCE


all: Makefile libtest.so

dist: distdir FORCE
	(cd `dirname $(DISTDIR)` && $(TAR) $(DISTNAME).tar $(DISTNAME) && $(COMPRESS) $(DISTNAME).tar) && $(MOVE) `dirname $(DISTDIR)`\$(DISTNAME).tar.gz . && $(DEL_FILE) -r $(DISTDIR)

distdir: FORCE
	@if not exist $(DISTDIR) mkdir $(DISTDIR) & if not exist $(DISTDIR) exit 1
	$(COPY_FILE) --parents $(DIST) $(DISTDIR)\
	$(COPY_FILE) --parents ..\resources.qrc $(DISTDIR)\
	$(COPY_FILE) --parents G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\data\dummy.cpp $(DISTDIR)\
	$(COPY_FILE) --parents ..\widget.h ..\pulltorefreshlistwidget.h $(DISTDIR)\
	$(COPY_FILE) --parents ..\main.cpp ..\widget.cpp ..\pulltorefreshlistwidget.cpp $(DISTDIR)\
	$(COPY_FILE) --parents ..\widget.ui $(DISTDIR)\


clean: compiler_clean 
	-$(DEL_FILE) $(OBJECTS)
	-$(DEL_FILE) *~ core *.core


distclean: clean 
	-$(DEL_FILE) $(TARGET) 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) Makefile


####### Sub-libraries

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_rcc_make_all: qrc_resources.cpp
compiler_rcc_clean:
	-$(DEL_FILE) qrc_resources.cpp
qrc_resources.cpp: ..\resources.qrc \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\bin\rcc.exe \
		..\icons\loading.gif
	G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\bin\rcc.exe -name resources ..\resources.qrc -o qrc_resources.cpp

compiler_moc_predefs_make_all: moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) moc_predefs.h
moc_predefs.h: G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\data\dummy.cpp
	G:\android\android-ndk-r20b/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++ -D__ANDROID_API__=21 -target aarch64-none-linux-android -gcc-toolchain G:\android\android-ndk-r20b/toolchains/aarch64-linux-android-4.9/prebuilt/windows-x86_64 -fno-limit-debug-info -DANDROID_HAS_WSTRING --sysroot=G:\android\android-ndk-r20b/sysroot -isystem G:\android\android-ndk-r20b/sysroot/usr/include/aarch64-linux-android -isystem G:\android\android-ndk-r20b/sources/cxx-stl/llvm-libc++/include -isystem G:\android\android-ndk-r20b/sources/android/support/include -isystem G:\android\android-ndk-r20b/sources/cxx-stl/llvm-libc++abi/include -fstack-protector-strong -DANDROID -g -g -std=gnu++11 -Wall -W -dM -E -o moc_predefs.h G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: moc_widget.cpp moc_pulltorefreshlistwidget.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) moc_widget.cpp moc_pulltorefreshlistwidget.cpp
moc_widget.cpp: ..\widget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QWidget \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qtwidgetsglobal.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qtguiglobal.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qglobal.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qconfig-bootstrapped.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qconfig.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qtcore-config.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsystemdetection.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qprocessordetection.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcompilerdetection.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qtypeinfo.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsysinfo.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qlogging.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qflags.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qatomic.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qbasicatomic.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qatomic_bootstrap.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qgenericatomic.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qatomic_cxx11.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qatomic_msvc.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qglobalstatic.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qmutex.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qnumeric.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qversiontagging.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qtgui-config.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qtwidgets-config.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qwindowdefs.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qobjectdefs.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qnamespace.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qobjectdefs_impl.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qwindowdefs_win.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qobject.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstring.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qchar.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qbytearray.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qrefcount.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qarraydata.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringliteral.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringalgorithms.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringview.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringbuilder.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qlist.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qalgorithms.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qiterator.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qhashfunctions.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qpair.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qvector.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qpoint.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qbytearraylist.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringlist.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qregexp.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringmatcher.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcoreevent.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qscopedpointer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qmetatype.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qvarlengtharray.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcontainerfwd.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qobject_impl.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qmargins.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpaintdevice.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qrect.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsize.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpalette.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qcolor.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qrgb.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qrgba64.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qbrush.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qmatrix.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpolygon.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qregion.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qdatastream.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qiodevice.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qline.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qtransform.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpainterpath.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qimage.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpixelformat.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpixmap.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsharedpointer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qshareddata.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qhash.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsharedpointer_impl.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qfont.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qfontmetrics.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qfontinfo.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qsizepolicy.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qcursor.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qkeysequence.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qevent.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qvariant.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qmap.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qdebug.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qtextstream.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qlocale.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qset.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcontiguouscache.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qurl.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qurlquery.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qfile.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qfiledevice.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qvector2d.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qtouchdevice.h \
		..\pulltorefreshlistwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QListWidget \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlistwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlistview.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractitemview.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractscrollarea.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qframe.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qabstractitemmodel.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qitemselectionmodel.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractitemdelegate.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qstyleoption.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractspinbox.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qvalidator.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qregularexpression.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qicon.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qslider.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractslider.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qstyle.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qtabbar.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qtabwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qrubberband.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QLabel \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlabel.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\QMovie \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qmovie.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qimagereader.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcoreapplication.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qeventloop.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qimageiohandler.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qplugin.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qpointer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qjsonobject.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qjsonvalue.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qfactoryinterface.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QVBoxLayout \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qboxlayout.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlayout.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlayoutitem.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qgridlayout.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QScrollBar \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qscrollbar.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\QMouseEvent \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\QTimer \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qtimer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qbasictimer.h \
		moc_predefs.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\bin\moc.exe
	G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\bin\moc.exe $(DEFINES) --include F:/code/M2/test/test/Debug/moc_predefs.h -IG:/Qt/Qt5.13.2/5.13.2/android_arm64_v8a/mkspecs/android-clang -IF:/code/M2/test/test -IG:/Qt/Qt5.13.2/5.13.2/android_arm64_v8a/include -IG:/Qt/Qt5.13.2/5.13.2/android_arm64_v8a/include/QtWidgets -IG:/Qt/Qt5.13.2/5.13.2/android_arm64_v8a/include/QtGui -IG:/Qt/Qt5.13.2/5.13.2/android_arm64_v8a/include/QtCore -I. -IG:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++ -IG:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/i686-w64-mingw32 -IG:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/backward -IG:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include -IG:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include-fixed -IG:/Qt/Qt5.13.2/Tools/mingw730_32/i686-w64-mingw32/include ..\widget.h -o moc_widget.cpp

moc_pulltorefreshlistwidget.cpp: ..\pulltorefreshlistwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QListWidget \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlistwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qtwidgetsglobal.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qtguiglobal.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qglobal.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qconfig-bootstrapped.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qconfig.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qtcore-config.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsystemdetection.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qprocessordetection.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcompilerdetection.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qtypeinfo.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsysinfo.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qlogging.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qflags.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qatomic.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qbasicatomic.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qatomic_bootstrap.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qgenericatomic.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qatomic_cxx11.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qatomic_msvc.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qglobalstatic.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qmutex.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qnumeric.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qversiontagging.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qtgui-config.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qtwidgets-config.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlistview.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractitemview.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractscrollarea.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qframe.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qwindowdefs.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qobjectdefs.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qnamespace.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qobjectdefs_impl.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qwindowdefs_win.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qobject.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstring.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qchar.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qbytearray.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qrefcount.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qarraydata.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringliteral.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringalgorithms.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringview.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringbuilder.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qlist.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qalgorithms.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qiterator.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qhashfunctions.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qpair.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qvector.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qpoint.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qbytearraylist.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringlist.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qregexp.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringmatcher.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcoreevent.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qscopedpointer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qmetatype.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qvarlengtharray.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcontainerfwd.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qobject_impl.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qmargins.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpaintdevice.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qrect.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsize.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpalette.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qcolor.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qrgb.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qrgba64.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qbrush.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qmatrix.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpolygon.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qregion.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qdatastream.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qiodevice.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qline.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qtransform.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpainterpath.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qimage.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpixelformat.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpixmap.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsharedpointer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qshareddata.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qhash.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsharedpointer_impl.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qfont.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qfontmetrics.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qfontinfo.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qsizepolicy.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qcursor.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qkeysequence.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qevent.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qvariant.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qmap.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qdebug.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qtextstream.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qlocale.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qset.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcontiguouscache.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qurl.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qurlquery.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qfile.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qfiledevice.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qvector2d.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qtouchdevice.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qabstractitemmodel.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qitemselectionmodel.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractitemdelegate.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qstyleoption.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractspinbox.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qvalidator.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qregularexpression.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qicon.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qslider.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractslider.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qstyle.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qtabbar.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qtabwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qrubberband.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QLabel \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlabel.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\QMovie \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qmovie.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qimagereader.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcoreapplication.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qeventloop.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qimageiohandler.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qplugin.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qpointer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qjsonobject.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qjsonvalue.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qfactoryinterface.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QVBoxLayout \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qboxlayout.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlayout.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlayoutitem.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qgridlayout.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QScrollBar \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qscrollbar.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\QMouseEvent \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\QTimer \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qtimer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qbasictimer.h \
		moc_predefs.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\bin\moc.exe
	G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\bin\moc.exe $(DEFINES) --include F:/code/M2/test/test/Debug/moc_predefs.h -IG:/Qt/Qt5.13.2/5.13.2/android_arm64_v8a/mkspecs/android-clang -IF:/code/M2/test/test -IG:/Qt/Qt5.13.2/5.13.2/android_arm64_v8a/include -IG:/Qt/Qt5.13.2/5.13.2/android_arm64_v8a/include/QtWidgets -IG:/Qt/Qt5.13.2/5.13.2/android_arm64_v8a/include/QtGui -IG:/Qt/Qt5.13.2/5.13.2/android_arm64_v8a/include/QtCore -I. -IG:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++ -IG:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/i686-w64-mingw32 -IG:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/backward -IG:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include -IG:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include-fixed -IG:/Qt/Qt5.13.2/Tools/mingw730_32/i686-w64-mingw32/include ..\pulltorefreshlistwidget.h -o moc_pulltorefreshlistwidget.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_widget.h
compiler_uic_clean:
	-$(DEL_FILE) ui_widget.h
ui_widget.h: ..\widget.ui \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\bin\uic.exe
	G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\bin\uic.exe ..\widget.ui -o ui_widget.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 

####### Compile

main.obj: ..\main.cpp ..\widget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QWidget \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qtwidgetsglobal.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qtguiglobal.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qglobal.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qconfig-bootstrapped.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qconfig.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qtcore-config.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsystemdetection.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qprocessordetection.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcompilerdetection.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qtypeinfo.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsysinfo.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qlogging.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qflags.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qatomic.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qbasicatomic.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qatomic_bootstrap.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qgenericatomic.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qatomic_cxx11.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qatomic_msvc.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qglobalstatic.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qmutex.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qnumeric.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qversiontagging.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qtgui-config.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qtwidgets-config.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qwindowdefs.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qobjectdefs.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qnamespace.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qobjectdefs_impl.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qwindowdefs_win.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qobject.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstring.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qchar.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qbytearray.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qrefcount.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qarraydata.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringliteral.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringalgorithms.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringview.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringbuilder.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qlist.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qalgorithms.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qiterator.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qhashfunctions.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qpair.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qvector.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qpoint.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qbytearraylist.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringlist.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qregexp.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringmatcher.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcoreevent.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qscopedpointer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qmetatype.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qvarlengtharray.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcontainerfwd.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qobject_impl.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qmargins.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpaintdevice.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qrect.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsize.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpalette.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qcolor.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qrgb.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qrgba64.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qbrush.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qmatrix.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpolygon.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qregion.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qdatastream.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qiodevice.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qline.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qtransform.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpainterpath.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qimage.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpixelformat.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpixmap.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsharedpointer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qshareddata.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qhash.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsharedpointer_impl.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qfont.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qfontmetrics.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qfontinfo.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qsizepolicy.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qcursor.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qkeysequence.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qevent.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qvariant.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qmap.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qdebug.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qtextstream.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qlocale.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qset.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcontiguouscache.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qurl.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qurlquery.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qfile.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qfiledevice.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qvector2d.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qtouchdevice.h \
		..\pulltorefreshlistwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QListWidget \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlistwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlistview.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractitemview.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractscrollarea.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qframe.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qabstractitemmodel.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qitemselectionmodel.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractitemdelegate.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qstyleoption.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractspinbox.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qvalidator.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qregularexpression.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qicon.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qslider.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractslider.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qstyle.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qtabbar.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qtabwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qrubberband.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QLabel \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlabel.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\QMovie \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qmovie.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qimagereader.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcoreapplication.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qeventloop.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qimageiohandler.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qplugin.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qpointer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qjsonobject.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qjsonvalue.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qfactoryinterface.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QVBoxLayout \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qboxlayout.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlayout.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlayoutitem.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qgridlayout.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QScrollBar \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qscrollbar.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\QMouseEvent \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\QTimer \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qtimer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qbasictimer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QApplication \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qapplication.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qdesktopwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qguiapplication.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qinputmethod.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o main.obj ..\main.cpp

widget.obj: ..\widget.cpp ..\widget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QWidget \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qtwidgetsglobal.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qtguiglobal.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qglobal.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qconfig-bootstrapped.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qconfig.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qtcore-config.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsystemdetection.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qprocessordetection.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcompilerdetection.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qtypeinfo.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsysinfo.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qlogging.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qflags.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qatomic.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qbasicatomic.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qatomic_bootstrap.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qgenericatomic.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qatomic_cxx11.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qatomic_msvc.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qglobalstatic.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qmutex.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qnumeric.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qversiontagging.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qtgui-config.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qtwidgets-config.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qwindowdefs.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qobjectdefs.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qnamespace.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qobjectdefs_impl.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qwindowdefs_win.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qobject.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstring.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qchar.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qbytearray.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qrefcount.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qarraydata.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringliteral.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringalgorithms.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringview.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringbuilder.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qlist.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qalgorithms.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qiterator.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qhashfunctions.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qpair.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qvector.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qpoint.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qbytearraylist.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringlist.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qregexp.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringmatcher.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcoreevent.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qscopedpointer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qmetatype.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qvarlengtharray.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcontainerfwd.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qobject_impl.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qmargins.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpaintdevice.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qrect.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsize.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpalette.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qcolor.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qrgb.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qrgba64.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qbrush.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qmatrix.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpolygon.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qregion.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qdatastream.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qiodevice.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qline.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qtransform.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpainterpath.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qimage.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpixelformat.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpixmap.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsharedpointer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qshareddata.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qhash.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsharedpointer_impl.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qfont.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qfontmetrics.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qfontinfo.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qsizepolicy.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qcursor.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qkeysequence.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qevent.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qvariant.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qmap.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qdebug.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qtextstream.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qlocale.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qset.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcontiguouscache.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qurl.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qurlquery.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qfile.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qfiledevice.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qvector2d.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qtouchdevice.h \
		..\pulltorefreshlistwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QListWidget \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlistwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlistview.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractitemview.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractscrollarea.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qframe.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qabstractitemmodel.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qitemselectionmodel.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractitemdelegate.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qstyleoption.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractspinbox.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qvalidator.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qregularexpression.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qicon.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qslider.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractslider.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qstyle.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qtabbar.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qtabwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qrubberband.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QLabel \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlabel.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\QMovie \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qmovie.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qimagereader.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcoreapplication.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qeventloop.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qimageiohandler.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qplugin.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qpointer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qjsonobject.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qjsonvalue.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qfactoryinterface.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QVBoxLayout \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qboxlayout.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlayout.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlayoutitem.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qgridlayout.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QScrollBar \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qscrollbar.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\QMouseEvent \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\QTimer \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qtimer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qbasictimer.h \
		ui_widget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o widget.obj ..\widget.cpp

pulltorefreshlistwidget.obj: ..\pulltorefreshlistwidget.cpp ..\pulltorefreshlistwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QListWidget \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlistwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qtwidgetsglobal.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qtguiglobal.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qglobal.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qconfig-bootstrapped.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qconfig.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qtcore-config.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsystemdetection.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qprocessordetection.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcompilerdetection.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qtypeinfo.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsysinfo.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qlogging.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qflags.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qatomic.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qbasicatomic.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qatomic_bootstrap.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qgenericatomic.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qatomic_cxx11.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qatomic_msvc.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qglobalstatic.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qmutex.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qnumeric.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qversiontagging.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qtgui-config.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qtwidgets-config.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlistview.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractitemview.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractscrollarea.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qframe.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qwindowdefs.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qobjectdefs.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qnamespace.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qobjectdefs_impl.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qwindowdefs_win.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qobject.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstring.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qchar.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qbytearray.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qrefcount.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qarraydata.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringliteral.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringalgorithms.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringview.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringbuilder.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qlist.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qalgorithms.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qiterator.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qhashfunctions.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qpair.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qvector.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qpoint.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qbytearraylist.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringlist.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qregexp.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qstringmatcher.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcoreevent.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qscopedpointer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qmetatype.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qvarlengtharray.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcontainerfwd.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qobject_impl.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qmargins.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpaintdevice.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qrect.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsize.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpalette.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qcolor.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qrgb.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qrgba64.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qbrush.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qmatrix.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpolygon.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qregion.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qdatastream.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qiodevice.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qline.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qtransform.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpainterpath.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qimage.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpixelformat.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qpixmap.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsharedpointer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qshareddata.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qhash.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qsharedpointer_impl.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qfont.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qfontmetrics.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qfontinfo.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qsizepolicy.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qcursor.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qkeysequence.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qevent.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qvariant.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qmap.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qdebug.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qtextstream.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qlocale.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qset.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcontiguouscache.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qurl.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qurlquery.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qfile.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qfiledevice.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qvector2d.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qtouchdevice.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qabstractitemmodel.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qitemselectionmodel.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractitemdelegate.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qstyleoption.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractspinbox.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qvalidator.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qregularexpression.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qicon.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qslider.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qabstractslider.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qstyle.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qtabbar.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qtabwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qrubberband.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QLabel \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlabel.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\QMovie \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qmovie.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qimagereader.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qcoreapplication.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qeventloop.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qimageiohandler.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qplugin.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qpointer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qjsonobject.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qjsonvalue.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qfactoryinterface.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QVBoxLayout \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qboxlayout.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlayout.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qlayoutitem.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qgridlayout.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QScrollBar \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qscrollbar.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\QMouseEvent \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\QTimer \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qtimer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\qbasictimer.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QApplication \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qapplication.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\qdesktopwidget.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qguiapplication.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtGui\qinputmethod.h \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtWidgets\QStyle \
		G:\Qt\Qt5.13.2\5.13.2\android_arm64_v8a\include\QtCore\QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o pulltorefreshlistwidget.obj ..\pulltorefreshlistwidget.cpp

qrc_resources.obj: qrc_resources.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qrc_resources.obj qrc_resources.cpp

moc_widget.obj: moc_widget.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_widget.obj moc_widget.cpp

moc_pulltorefreshlistwidget.obj: moc_pulltorefreshlistwidget.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_pulltorefreshlistwidget.obj moc_pulltorefreshlistwidget.cpp

####### Install

install_target: first FORCE
	@if not exist $(INSTALL_ROOT:@msyshack@%=%)\libs\arm64-v8a mkdir $(INSTALL_ROOT:@msyshack@%=%)\libs\arm64-v8a & if not exist $(INSTALL_ROOT:@msyshack@%=%)\libs\arm64-v8a exit 1
	-$(QINSTALL_PROGRAM) $(QMAKE_TARGET) $(INSTALL_ROOT:@msyshack@%=%)\libs\arm64-v8a\$(QMAKE_TARGET)

uninstall_target: FORCE
	-$(DEL_FILE) $(INSTALL_ROOT:@msyshack@%=%)\libs\arm64-v8a\$(QMAKE_TARGET)
	-$(DEL_DIR) $(INSTALL_ROOT:@msyshack@%=%)\libs\arm64-v8a 


install: install_target  FORCE

uninstall: uninstall_target  FORCE

FORCE:

