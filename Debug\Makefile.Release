#############################################################################
# Makefile for building: test
# Generated by qmake (3.1) (Qt 5.13.2)
# Project:  ..\test.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Release

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -DQT_DEPRECATED_WARNINGS -DQT_QML_DEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -O2 -Wall -W -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -O2 -std=gnu++11 -Wall -W -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I..\..\test -I. -IG:\Qt\Qt5.13.2\5.13.2\mingw73_32\include -IG:\Qt\Qt5.13.2\5.13.2\mingw73_32\include\QtWidgets -IG:\Qt\Qt5.13.2\5.13.2\mingw73_32\include\QtGui -IG:\Qt\Qt5.13.2\5.13.2\mingw73_32\include\QtANGLE -IG:\Qt\Qt5.13.2\5.13.2\mingw73_32\include\QtCore -Irelease -I. -IG:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-s -Wl,-subsystem,windows -mthreads
LIBS        =        G:\Qt\Qt5.13.2\5.13.2\mingw73_32\lib\libQt5Widgets.a G:\Qt\Qt5.13.2\5.13.2\mingw73_32\lib\libQt5Gui.a G:\Qt\Qt5.13.2\5.13.2\mingw73_32\lib\libQt5Core.a  -lmingw32 G:\Qt\Qt5.13.2\5.13.2\mingw73_32\lib\libqtmain.a -LC:\openssl\lib -LC:\Utils\my_sql\mysql-5.7.25-win32\lib -LC:\Utils\postgresql\pgsql\lib -lshell32 
QMAKE         = G:\Qt\Qt5.13.2\5.13.2\mingw73_32\bin\qmake.exe
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
COPY          = copy /y
SED           = $(QMAKE) -install sed
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
DEL_FILE      = del
DEL_DIR       = rmdir
MOVE          = move
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
INSTALL_FILE    = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR     = xcopy /s /q /y /i
QINSTALL        = G:\Qt\Qt5.13.2\5.13.2\mingw73_32\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = G:\Qt\Qt5.13.2\5.13.2\mingw73_32\bin\qmake.exe -install qinstall -exe

####### Output directory

OBJECTS_DIR   = release

####### Files

SOURCES       = ..\main.cpp \
		..\widget.cpp \
		..\pulltorefreshlistwidget.cpp release\qrc_resources.cpp \
		release\moc_widget.cpp \
		release\moc_pulltorefreshlistwidget.cpp
OBJECTS       = release/main.o \
		release/widget.o \
		release/pulltorefreshlistwidget.o \
		release/qrc_resources.o \
		release/moc_widget.o \
		release/moc_pulltorefreshlistwidget.o

DIST          =  ..\widget.h \
		..\pulltorefreshlistwidget.h ..\main.cpp \
		..\widget.cpp \
		..\pulltorefreshlistwidget.cpp
QMAKE_TARGET  = test
DESTDIR        = release\ #avoid trailing-slash linebreak
TARGET         = test.exe
DESTDIR_TARGET = release\test.exe

####### Build rules

first: all
all: Makefile.Release  release/test.exe

release/test.exe: G:/Qt/Qt5.13.2/5.13.2/mingw73_32/lib/libQt5Widgets.a G:/Qt/Qt5.13.2/5.13.2/mingw73_32/lib/libQt5Gui.a G:/Qt/Qt5.13.2/5.13.2/mingw73_32/lib/libQt5Core.a G:/Qt/Qt5.13.2/5.13.2/mingw73_32/lib/libqtmain.a ui_widget.h $(OBJECTS) 
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) $(OBJECTS)  $(LIBS)

qmake: FORCE
	@$(QMAKE) -o Makefile.Release ..\test.pro -spec win32-g++ "CONFIG+=debug" "CONFIG+=qml_debug"

qmake_all: FORCE

dist:
	$(ZIP) test.zip $(SOURCES) $(DIST) ..\test.pro G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\spec_pre.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\qdevice.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\device_config.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\common\sanitize.conf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\common\gcc-base.conf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\common\g++-base.conf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\common\angle.conf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\win32\windows_vulkan_sdk.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\common\windows-vulkan.conf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\common\g++-win32.conf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\common\windows-desktop.conf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\qconfig.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3danimation.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3danimation_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dcore.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dcore_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dextras.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dextras_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dinput.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dinput_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dlogic.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dlogic_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquick.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquick_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquickanimation.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquickanimation_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquickextras.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquickextras_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquickinput.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquickinput_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquickrender.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquickrender_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquickscene2d.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquickscene2d_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3drender.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3drender_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_accessibility_support_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_axbase.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_axbase_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_axcontainer.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_axcontainer_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_axserver.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_axserver_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_bluetooth.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_bluetooth_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_bodymovin_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_bootstrap_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_charts.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_charts_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_concurrent.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_concurrent_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_core.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_core_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_datavisualization.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_datavisualization_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_dbus.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_dbus_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_designer.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_designer_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_designercomponents_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_devicediscovery_support_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_edid_support_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_egl_support_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_eventdispatcher_support_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_fb_support_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_fontdatabase_support_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_gamepad.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_gamepad_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_gui.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_gui_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_help.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_help_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_location.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_location_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_multimedia.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_multimedia_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_multimediawidgets.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_multimediawidgets_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_network.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_network_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_networkauth.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_networkauth_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_nfc.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_nfc_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_opengl.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_opengl_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_openglextensions.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_openglextensions_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_packetprotocol_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_platformcompositor_support_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_positioning.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_positioning_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_positioningquick.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_positioningquick_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_printsupport.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_printsupport_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_purchasing.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_purchasing_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_qml.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_qml_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_qmldebug_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_qmldevtools_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_qmltest.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_qmltest_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_qtmultimediaquicktools_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_quick.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_quick_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_quickcontrols2.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_quickcontrols2_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_quickparticles_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_quickshapes_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_quicktemplates2.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_quicktemplates2_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_quickwidgets.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_quickwidgets_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_remoteobjects.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_remoteobjects_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_repparser.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_repparser_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_script.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_script_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_scripttools.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_scripttools_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_scxml.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_scxml_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_sensors.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_sensors_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_serialbus.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_serialbus_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_serialport.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_serialport_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_sql.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_sql_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_svg.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_svg_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_testlib.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_testlib_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_texttospeech.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_texttospeech_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_theme_support_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_uiplugin.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_uitools.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_uitools_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_virtualkeyboard.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_virtualkeyboard_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_webchannel.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_webchannel_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_websockets.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_websockets_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_widgets.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_widgets_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_windowsuiautomation_support_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_winextras.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_winextras_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_xml.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_xml_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_xmlpatterns.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_xmlpatterns_private.pri G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\qt_functions.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\qt_config.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\win32-g++\qmake.conf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\spec_post.prf .qmake.stash G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\exclusive_builds.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\toolchain.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\default_pre.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\win32\default_pre.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\resolve_config.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\exclusive_builds_post.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\default_post.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\build_pass.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\qml_debug.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\precompile_header.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\warn_on.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\qt.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\resources.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\moc.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\win32\opengl.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\uic.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\qmake_use.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\file_copies.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\win32\windows.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\testcase_targets.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\exceptions.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\yacc.prf G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\lex.prf ..\test.pro ..\resources.qrc G:\Qt\Qt5.13.2\5.13.2\mingw73_32\lib\Qt5Widgets.prl G:\Qt\Qt5.13.2\5.13.2\mingw73_32\lib\Qt5Gui.prl G:\Qt\Qt5.13.2\5.13.2\mingw73_32\lib\Qt5Core.prl G:\Qt\Qt5.13.2\5.13.2\mingw73_32\lib\qtmain.prl   ..\resources.qrc G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\data\dummy.cpp ..\widget.h ..\pulltorefreshlistwidget.h  ..\main.cpp ..\widget.cpp ..\pulltorefreshlistwidget.cpp ..\widget.ui    

clean: compiler_clean 
	-$(DEL_FILE) release\main.o release\widget.o release\pulltorefreshlistwidget.o release\qrc_resources.o release\moc_widget.o release\moc_pulltorefreshlistwidget.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Release

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all: release/qrc_resources.cpp
compiler_rcc_clean:
	-$(DEL_FILE) release\qrc_resources.cpp
release/qrc_resources.cpp: ../resources.qrc \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/bin/rcc.exe \
		../icons/loading.gif
	G:\Qt\Qt5.13.2\5.13.2\mingw73_32\bin\rcc.exe -name resources ..\resources.qrc -o release\qrc_resources.cpp

compiler_moc_predefs_make_all: release/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) release\moc_predefs.h
release/moc_predefs.h: G:/Qt/Qt5.13.2/5.13.2/mingw73_32/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -O2 -std=gnu++11 -Wall -W -Wextra -dM -E -o release\moc_predefs.h G:\Qt\Qt5.13.2\5.13.2\mingw73_32\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: release/moc_widget.cpp release/moc_pulltorefreshlistwidget.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) release\moc_widget.cpp release\moc_pulltorefreshlistwidget.cpp
release/moc_widget.cpp: ../widget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QWidget \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qtguiglobal.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qglobal.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qtcore-config.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsystemdetection.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qprocessordetection.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qtypeinfo.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsysinfo.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qlogging.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qflags.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qbasicatomic.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qgenericatomic.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qglobalstatic.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qmutex.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qnumeric.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qversiontagging.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qtgui-config.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qnamespace.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qobject.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstring.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qchar.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearray.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qrefcount.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydata.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringliteral.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringview.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringbuilder.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qlist.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qalgorithms.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qiterator.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qhashfunctions.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qpair.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qvector.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qpoint.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearraylist.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringlist.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qregexp.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringmatcher.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreevent.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qscopedpointer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qmetatype.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qobject_impl.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qmargins.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintdevice.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qrect.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsize.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpalette.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qcolor.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qrgb.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qrgba64.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qbrush.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qmatrix.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpolygon.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qregion.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qdatastream.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qiodevice.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qline.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qtransform.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpainterpath.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qimage.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpixelformat.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpixmap.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qshareddata.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qhash.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qfont.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qfontmetrics.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qfontinfo.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qcursor.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qkeysequence.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qevent.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qvariant.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qmap.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qdebug.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qtextstream.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qlocale.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qset.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qurl.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qurlquery.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qfile.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qfiledevice.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qvector2d.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qtouchdevice.h \
		../pulltorefreshlistwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QListWidget \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistview.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemview.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qframe.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractitemmodel.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qitemselectionmodel.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemdelegate.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleoption.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qvalidator.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qregularexpression.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qicon.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qslider.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyle.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabbar.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qrubberband.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QLabel \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlabel.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/QMovie \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qmovie.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qimagereader.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreapplication.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qeventloop.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qimageiohandler.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qplugin.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qpointer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonobject.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonvalue.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qfactoryinterface.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QVBoxLayout \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayout.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QScrollBar \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollbar.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/QMouseEvent \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/QTimer \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qtimer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qbasictimer.h \
		release/moc_predefs.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/bin/moc.exe
	G:\Qt\Qt5.13.2\5.13.2\mingw73_32\bin\moc.exe $(DEFINES) --include F:/code/M2/test/test/Debug/release/moc_predefs.h -IG:/Qt/Qt5.13.2/5.13.2/mingw73_32/mkspecs/win32-g++ -IF:/code/M2/test/test -IG:/Qt/Qt5.13.2/5.13.2/mingw73_32/include -IG:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets -IG:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui -IG:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtANGLE -IG:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore -I. -IG:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++ -IG:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/i686-w64-mingw32 -IG:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/backward -IG:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include -IG:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include-fixed -IG:/Qt/Qt5.13.2/Tools/mingw730_32/i686-w64-mingw32/include ..\widget.h -o release\moc_widget.cpp

release/moc_pulltorefreshlistwidget.cpp: ../pulltorefreshlistwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QListWidget \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qtguiglobal.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qglobal.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qtcore-config.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsystemdetection.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qprocessordetection.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qtypeinfo.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsysinfo.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qlogging.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qflags.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qbasicatomic.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qgenericatomic.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qglobalstatic.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qmutex.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qnumeric.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qversiontagging.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qtgui-config.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistview.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemview.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qframe.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qnamespace.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qobject.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstring.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qchar.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearray.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qrefcount.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydata.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringliteral.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringview.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringbuilder.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qlist.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qalgorithms.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qiterator.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qhashfunctions.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qpair.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qvector.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qpoint.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearraylist.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringlist.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qregexp.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringmatcher.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreevent.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qscopedpointer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qmetatype.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qobject_impl.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qmargins.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintdevice.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qrect.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsize.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpalette.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qcolor.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qrgb.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qrgba64.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qbrush.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qmatrix.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpolygon.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qregion.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qdatastream.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qiodevice.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qline.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qtransform.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpainterpath.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qimage.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpixelformat.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpixmap.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qshareddata.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qhash.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qfont.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qfontmetrics.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qfontinfo.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qcursor.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qkeysequence.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qevent.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qvariant.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qmap.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qdebug.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qtextstream.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qlocale.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qset.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qurl.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qurlquery.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qfile.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qfiledevice.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qvector2d.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qtouchdevice.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractitemmodel.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qitemselectionmodel.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemdelegate.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleoption.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qvalidator.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qregularexpression.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qicon.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qslider.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyle.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabbar.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qrubberband.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QLabel \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlabel.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/QMovie \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qmovie.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qimagereader.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreapplication.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qeventloop.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qimageiohandler.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qplugin.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qpointer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonobject.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonvalue.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qfactoryinterface.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QVBoxLayout \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayout.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QScrollBar \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollbar.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/QMouseEvent \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/QTimer \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qtimer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qbasictimer.h \
		release/moc_predefs.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/bin/moc.exe
	G:\Qt\Qt5.13.2\5.13.2\mingw73_32\bin\moc.exe $(DEFINES) --include F:/code/M2/test/test/Debug/release/moc_predefs.h -IG:/Qt/Qt5.13.2/5.13.2/mingw73_32/mkspecs/win32-g++ -IF:/code/M2/test/test -IG:/Qt/Qt5.13.2/5.13.2/mingw73_32/include -IG:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets -IG:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui -IG:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtANGLE -IG:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore -I. -IG:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++ -IG:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/i686-w64-mingw32 -IG:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/backward -IG:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include -IG:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include-fixed -IG:/Qt/Qt5.13.2/Tools/mingw730_32/i686-w64-mingw32/include ..\pulltorefreshlistwidget.h -o release\moc_pulltorefreshlistwidget.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_widget.h
compiler_uic_clean:
	-$(DEL_FILE) ui_widget.h
ui_widget.h: ../widget.ui \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/bin/uic.exe
	G:\Qt\Qt5.13.2\5.13.2\mingw73_32\bin\uic.exe ..\widget.ui -o ui_widget.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 



####### Compile

release/main.o: ../main.cpp ../widget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QWidget \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qtguiglobal.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qglobal.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qtcore-config.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsystemdetection.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qprocessordetection.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qtypeinfo.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsysinfo.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qlogging.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qflags.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qbasicatomic.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qgenericatomic.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qglobalstatic.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qmutex.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qnumeric.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qversiontagging.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qtgui-config.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qnamespace.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qobject.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstring.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qchar.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearray.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qrefcount.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydata.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringliteral.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringview.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringbuilder.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qlist.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qalgorithms.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qiterator.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qhashfunctions.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qpair.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qvector.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qpoint.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearraylist.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringlist.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qregexp.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringmatcher.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreevent.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qscopedpointer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qmetatype.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qobject_impl.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qmargins.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintdevice.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qrect.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsize.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpalette.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qcolor.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qrgb.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qrgba64.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qbrush.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qmatrix.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpolygon.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qregion.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qdatastream.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qiodevice.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qline.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qtransform.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpainterpath.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qimage.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpixelformat.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpixmap.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qshareddata.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qhash.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qfont.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qfontmetrics.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qfontinfo.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qcursor.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qkeysequence.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qevent.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qvariant.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qmap.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qdebug.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qtextstream.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qlocale.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qset.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qurl.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qurlquery.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qfile.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qfiledevice.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qvector2d.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qtouchdevice.h \
		../pulltorefreshlistwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QListWidget \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistview.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemview.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qframe.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractitemmodel.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qitemselectionmodel.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemdelegate.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleoption.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qvalidator.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qregularexpression.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qicon.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qslider.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyle.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabbar.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qrubberband.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QLabel \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlabel.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/QMovie \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qmovie.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qimagereader.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreapplication.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qeventloop.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qimageiohandler.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qplugin.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qpointer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonobject.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonvalue.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qfactoryinterface.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QVBoxLayout \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayout.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QScrollBar \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollbar.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/QMouseEvent \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/QTimer \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qtimer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qbasictimer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QApplication \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qapplication.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdesktopwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qguiapplication.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qinputmethod.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\main.o ..\main.cpp

release/widget.o: ../widget.cpp ../widget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QWidget \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qtguiglobal.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qglobal.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qtcore-config.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsystemdetection.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qprocessordetection.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qtypeinfo.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsysinfo.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qlogging.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qflags.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qbasicatomic.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qgenericatomic.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qglobalstatic.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qmutex.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qnumeric.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qversiontagging.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qtgui-config.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qnamespace.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qobject.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstring.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qchar.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearray.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qrefcount.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydata.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringliteral.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringview.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringbuilder.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qlist.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qalgorithms.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qiterator.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qhashfunctions.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qpair.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qvector.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qpoint.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearraylist.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringlist.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qregexp.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringmatcher.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreevent.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qscopedpointer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qmetatype.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qobject_impl.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qmargins.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintdevice.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qrect.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsize.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpalette.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qcolor.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qrgb.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qrgba64.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qbrush.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qmatrix.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpolygon.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qregion.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qdatastream.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qiodevice.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qline.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qtransform.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpainterpath.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qimage.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpixelformat.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpixmap.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qshareddata.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qhash.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qfont.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qfontmetrics.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qfontinfo.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qcursor.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qkeysequence.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qevent.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qvariant.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qmap.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qdebug.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qtextstream.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qlocale.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qset.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qurl.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qurlquery.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qfile.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qfiledevice.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qvector2d.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qtouchdevice.h \
		../pulltorefreshlistwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QListWidget \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistview.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemview.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qframe.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractitemmodel.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qitemselectionmodel.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemdelegate.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleoption.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qvalidator.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qregularexpression.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qicon.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qslider.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyle.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabbar.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qrubberband.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QLabel \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlabel.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/QMovie \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qmovie.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qimagereader.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreapplication.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qeventloop.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qimageiohandler.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qplugin.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qpointer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonobject.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonvalue.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qfactoryinterface.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QVBoxLayout \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayout.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QScrollBar \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollbar.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/QMouseEvent \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/QTimer \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qtimer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qbasictimer.h \
		ui_widget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\widget.o ..\widget.cpp

release/pulltorefreshlistwidget.o: ../pulltorefreshlistwidget.cpp ../pulltorefreshlistwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QListWidget \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qtguiglobal.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qglobal.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qtcore-config.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsystemdetection.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qprocessordetection.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qtypeinfo.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsysinfo.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qlogging.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qflags.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qbasicatomic.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qgenericatomic.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qglobalstatic.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qmutex.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qnumeric.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qversiontagging.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qtgui-config.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistview.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemview.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qframe.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qnamespace.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qobject.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstring.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qchar.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearray.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qrefcount.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydata.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringliteral.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringview.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringbuilder.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qlist.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qalgorithms.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qiterator.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qhashfunctions.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qpair.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qvector.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qpoint.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearraylist.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringlist.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qregexp.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qstringmatcher.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreevent.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qscopedpointer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qmetatype.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qobject_impl.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qmargins.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintdevice.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qrect.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsize.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpalette.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qcolor.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qrgb.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qrgba64.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qbrush.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qmatrix.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpolygon.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qregion.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qdatastream.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qiodevice.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qline.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qtransform.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpainterpath.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qimage.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpixelformat.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qpixmap.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qshareddata.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qhash.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qfont.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qfontmetrics.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qfontinfo.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qcursor.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qkeysequence.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qevent.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qvariant.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qmap.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qdebug.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qtextstream.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qlocale.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qset.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qurl.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qurlquery.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qfile.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qfiledevice.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qvector2d.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qtouchdevice.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractitemmodel.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qitemselectionmodel.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemdelegate.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleoption.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qvalidator.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qregularexpression.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qicon.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qslider.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyle.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabbar.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qrubberband.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QLabel \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlabel.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/QMovie \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qmovie.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qimagereader.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreapplication.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qeventloop.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qimageiohandler.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qplugin.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qpointer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonobject.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonvalue.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qfactoryinterface.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QVBoxLayout \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayout.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QScrollBar \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollbar.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/QMouseEvent \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/QTimer \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qtimer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/qbasictimer.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QApplication \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qapplication.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdesktopwidget.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qguiapplication.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtGui/qinputmethod.h \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtWidgets/QStyle \
		G:/Qt/Qt5.13.2/5.13.2/mingw73_32/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\pulltorefreshlistwidget.o ..\pulltorefreshlistwidget.cpp

release/qrc_resources.o: release/qrc_resources.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\qrc_resources.o release\qrc_resources.cpp

release/moc_widget.o: release/moc_widget.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_widget.o release\moc_widget.cpp

release/moc_pulltorefreshlistwidget.o: release/moc_pulltorefreshlistwidget.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_pulltorefreshlistwidget.o release\moc_pulltorefreshlistwidget.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

