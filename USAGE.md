# 使用说明 - 手势上滑加载更多列表组件

## 快速开始

### 1. 编译项目
```bash
qmake
make
```

### 2. 运行示例
运行编译后的程序，你会看到一个包含初始数据的列表。

### 3. 测试手势上滑加载更多功能

#### 触摸设备操作：
1. **滚动到底部**：用手指滑动将列表滚动到最底部
2. **向上滑动**：在列表底部继续向上滑动手指
3. **观察加载指示器**：当滑动距离足够时，会在底部显示加载动画
4. **释放触发加载**：抬起手指后，会触发加载更多数据的逻辑
5. **查看新数据**：2秒后会在列表底部添加3个新的数据项

#### 鼠标操作：
1. **滚动到底部**：使用鼠标滚轮或拖拽滚动条，将列表滚动到最底部
2. **向上拖拽**：在列表底部继续向上拖拽鼠标（按住左键）
3. **观察加载指示器**：当拖拽距离足够时，会在底部显示加载动画
4. **释放触发加载**：释放鼠标左键后，会触发加载更多数据的逻辑
5. **查看新数据**：2秒后会在列表底部添加3个新的数据项

## 自定义使用

### 继承并实现你自己的加载逻辑

```cpp
class MyCustomListWidget : public PullToRefreshListWidget
{
    Q_OBJECT

public:
    explicit MyCustomListWidget(QWidget *parent = nullptr)
        : PullToRefreshListWidget(parent)
    {
        // 设置自定义配置
        setRefreshThreshold(100);  // 设置触发阈值为100像素
        setLoadingText("加载中，请稍候...");

        // 添加初始数据
        loadInitialData();
    }

protected:
    void onRefreshRequested() override
    {
        // 实现你的加载逻辑
        // 例如：网络请求、数据库查询等

        // 模拟异步加载
        QTimer::singleShot(1000, this, [this]() {
            // 添加新数据
            for (int i = 0; i < 5; ++i) {
                addItem(QString("API数据 %1").arg(count() + 1));
            }

            // 重要：完成后必须调用
            finishRefresh();
        });
    }

private:
    void loadInitialData()
    {
        for (int i = 1; i <= 20; ++i) {
            addItem(QString("初始数据 %1").arg(i));
        }
    }
};
```

## 配置选项

### 设置触发阈值
```cpp
setRefreshThreshold(80);  // 默认80像素
```

### 自定义加载文本
```cpp
setLoadingText("正在获取更多内容...");
```

## 注意事项

1. **必须调用finishRefresh()**：在加载完成后必须调用此方法来隐藏加载指示器
2. **异步操作**：建议使用异步方式加载数据，避免阻塞UI
3. **错误处理**：在网络请求失败时也要调用`finishRefresh()`
4. **数据去重**：注意避免重复加载相同的数据

## 常见问题

### Q: 加载指示器不显示？
A: 确保列表已经滚动到底部，并且向上拖拽的距离超过了阈值（默认80像素）。

### Q: 加载完成后指示器不消失？
A: 检查是否在加载逻辑完成后调用了`finishRefresh()`方法。

### Q: 想要自定义加载动画？
A: 将自定义的loading.gif文件放在`icons/`目录下，确保文件名为`loading.gif`。

### Q: 如何禁用加载更多功能？
A: 可以在某些条件下不调用父类的鼠标事件处理函数，或者设置一个标志位来控制。

## 扩展功能

你可以基于这个组件实现：
- 分页加载
- 无限滚动
- 网络状态检测
- 加载失败重试
- 数据缓存机制
