#include "pulltorefreshlistwidget.h"
#include <QApplication>
#include <QStyle>
#include <QDebug>

const QString PullToRefreshListWidget::DEFAULT_LOADING_TEXT = "正在加载...";

PullToRefreshListWidget::PullToRefreshListWidget(QWidget *parent)
    : QListWidget(parent)
    , m_loadingWidget(nullptr)
    , m_loadingIcon(nullptr)
    , m_loadingText(nullptr)
    , m_loadingMovie(nullptr)
    , m_loadingLayout(nullptr)
    , m_isDragging(false)
    , m_isRefreshing(false)
    , m_refreshThreshold(DEFAULT_REFRESH_THRESHOLD)
    , m_pullDistance(0)
    , m_scrollTimer(new QTimer(this))
{
    setupLoadingIndicator();

    // 设置定时器检查滚动位置
    m_scrollTimer->setInterval(50); // 50ms检查一次
    connect(m_scrollTimer, &QTimer::timeout, this, &PullToRefreshListWidget::checkScrollPosition);

    // 启用鼠标跟踪
    setMouseTracking(true);
}

PullToRefreshListWidget::~PullToRefreshListWidget()
{
    if (m_loadingMovie) {
        delete m_loadingMovie;
    }
}

void PullToRefreshListWidget::setRefreshThreshold(int threshold)
{
    m_refreshThreshold = threshold;
}

void PullToRefreshListWidget::setLoadingText(const QString &text)
{
    if (m_loadingText) {
        m_loadingText->setText(text);
    }
}

void PullToRefreshListWidget::finishRefresh()
{
    m_isRefreshing = false;
    hideLoadingIndicator();
    m_pullDistance = 0;
}

void PullToRefreshListWidget::setupLoadingIndicator()
{
    // 创建加载指示器容器
    m_loadingWidget = new QWidget(this);
    m_loadingWidget->setFixedHeight(60);
    m_loadingWidget->hide();

    // 创建布局
    m_loadingLayout = new QVBoxLayout(m_loadingWidget);
    m_loadingLayout->setAlignment(Qt::AlignCenter);
    m_loadingLayout->setContentsMargins(0, 10, 0, 10);

    // 创建加载图标
    m_loadingIcon = new QLabel(m_loadingWidget);
    m_loadingIcon->setAlignment(Qt::AlignCenter);
    m_loadingIcon->setFixedSize(24, 24);

    // 创建旋转动画
    m_loadingMovie = new QMovie(":/icons/loading.gif"); // 你需要添加一个loading.gif资源
    if (!m_loadingMovie->isValid()) {
        // 如果没有gif资源，创建一个简单的文本指示器
        m_loadingIcon->setText("⟳");
        m_loadingIcon->setStyleSheet("font-size: 20px; color: #666;");
    } else {
        m_loadingIcon->setMovie(m_loadingMovie);
    }

    // 创建加载文本
    m_loadingText = new QLabel(DEFAULT_LOADING_TEXT, m_loadingWidget);
    m_loadingText->setAlignment(Qt::AlignCenter);
    m_loadingText->setStyleSheet("color: #666; font-size: 12px;");

    // 添加到布局
    m_loadingLayout->addWidget(m_loadingIcon);
    m_loadingLayout->addWidget(m_loadingText);

    updateLoadingIndicatorPosition();
}

void PullToRefreshListWidget::showLoadingIndicator()
{
    if (m_loadingWidget) {
        updateLoadingIndicatorPosition();
        m_loadingWidget->show();
        if (m_loadingMovie && m_loadingMovie->isValid()) {
            m_loadingMovie->start();
        }
    }
}

void PullToRefreshListWidget::hideLoadingIndicator()
{
    if (m_loadingWidget) {
        m_loadingWidget->hide();
        if (m_loadingMovie && m_loadingMovie->isValid()) {
            m_loadingMovie->stop();
        }
    }
}

void PullToRefreshListWidget::updateLoadingIndicatorPosition()
{
    if (m_loadingWidget) {
        int x = (width() - m_loadingWidget->width()) / 2;
        int y = -m_loadingWidget->height() + m_pullDistance;
        m_loadingWidget->move(x, y);
        m_loadingWidget->resize(width(), m_loadingWidget->height());
    }
}

void PullToRefreshListWidget::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_startPos = event->pos();
        m_isDragging = false;
        m_scrollTimer->start();
    }
    QListWidget::mousePressEvent(event);
}

void PullToRefreshListWidget::mouseMoveEvent(QMouseEvent *event)
{
    if (event->buttons() & Qt::LeftButton) {
        QPoint diff = event->pos() - m_startPos;

        // 只有在列表顶部且向下拖拽时才处理
        if (verticalScrollBar()->value() == 0 && diff.y() > 0) {
            m_isDragging = true;
            m_pullDistance = qMin(diff.y(), m_refreshThreshold * 2);

            if (m_pullDistance > 10) { // 最小拖拽距离
                showLoadingIndicator();
                updateLoadingIndicatorPosition();
            }

            // 阻止默认滚动行为
            return;
        }
    }
    QListWidget::mouseMoveEvent(event);
}

void PullToRefreshListWidget::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_scrollTimer->stop();

        if (m_isDragging && m_pullDistance >= m_refreshThreshold && !m_isRefreshing) {
            // 触发刷新
            m_isRefreshing = true;
            showLoadingIndicator();

            // 调用虚函数
            onRefreshRequested();
        } else {
            // 没有达到刷新阈值，隐藏指示器
            hideLoadingIndicator();
            m_pullDistance = 0;
        }

        m_isDragging = false;
    }
    QListWidget::mouseReleaseEvent(event);
}

void PullToRefreshListWidget::wheelEvent(QWheelEvent *event)
{
    // 如果正在刷新，阻止滚轮事件
    if (m_isRefreshing) {
        return;
    }
    QListWidget::wheelEvent(event);
}

void PullToRefreshListWidget::checkScrollPosition()
{
    // 检查是否在顶部，如果不在顶部且正在拖拽，则停止拖拽
    if (m_isDragging && verticalScrollBar()->value() > 0) {
        m_isDragging = false;
        hideLoadingIndicator();
        m_pullDistance = 0;
    }
}
