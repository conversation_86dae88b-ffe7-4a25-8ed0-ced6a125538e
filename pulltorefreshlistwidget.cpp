#include "pulltorefreshlistwidget.h"
#include <QApplication>
#include <QStyle>
#include <QDebug>
#include <QScroller>

const QString PullToRefreshListWidget::DEFAULT_LOADING_TEXT = "正在加载...";

PullToRefreshListWidget::PullToRefreshListWidget(QWidget *parent)
    : QListWidget(parent)
    , m_loadingWidget(nullptr)
    , m_loadingIcon(nullptr)
    , m_loadingText(nullptr)
    , m_loadingMovie(nullptr)
    , m_loadingLayout(nullptr)
    , m_isDragging(false)
    , m_isRefreshing(false)
    , m_refreshThreshold(DEFAULT_REFRESH_THRESHOLD)
    , m_pullDistance(0)
    , m_scrollTimer(new QTimer(this))
    , m_rotationTimer(new QTimer(this))
    , m_rotationAngle(0)
{
    setupLoadingIndicator();

    // 设置定时器检查滚动位置
    m_scrollTimer->setInterval(50); // 50ms检查一次
    connect(m_scrollTimer, &QTimer::timeout, this, &PullToRefreshListWidget::checkScrollPosition);

    // 设置旋转动画定时器
    m_rotationTimer->setInterval(50); // 50ms旋转一次
    connect(m_rotationTimer, &QTimer::timeout, this, &PullToRefreshListWidget::rotateLoadingIcon);

    // 启用鼠标跟踪
    setMouseTracking(true);

    setVerticalScrollMode(QListWidget::ScrollPerPixel);
    QScroller *pScroller = QScroller::scroller(this);
    pScroller->grabGesture(this,QScroller::LeftMouseButtonGesture);
}

PullToRefreshListWidget::~PullToRefreshListWidget()
{
    if (m_loadingMovie) {
        delete m_loadingMovie;
    }
}

void PullToRefreshListWidget::setRefreshThreshold(int threshold)
{
    m_refreshThreshold = threshold;
}

void PullToRefreshListWidget::setLoadingText(const QString &text)
{
    if (m_loadingText) {
        m_loadingText->setText(text);
    }
}

void PullToRefreshListWidget::finishRefresh()
{
    m_isRefreshing = false;
    hideLoadingIndicator();
    m_pullDistance = 0;
}

void PullToRefreshListWidget::setupLoadingIndicator()
{
    // 创建加载指示器容器
    m_loadingWidget = new QWidget(this);
    m_loadingWidget->setFixedHeight(60);
    m_loadingWidget->hide();

    // 创建布局
    m_loadingLayout = new QVBoxLayout(m_loadingWidget);
    m_loadingLayout->setAlignment(Qt::AlignCenter);
    m_loadingLayout->setContentsMargins(0, 10, 0, 10);

    // 创建加载图标
    m_loadingIcon = new QLabel(m_loadingWidget);
    m_loadingIcon->setAlignment(Qt::AlignCenter);
    m_loadingIcon->setFixedSize(24, 24);

    // 创建旋转动画
    m_loadingMovie = new QMovie(":/icons/loading.gif");
    if (!m_loadingMovie->isValid()) {
        // 如果没有gif资源，创建一个CSS样式的旋转指示器
        m_loadingIcon->setText("");
        m_loadingIcon->setStyleSheet(
            "border: 2px solid #f3f3f3; "
            "border-top: 2px solid #3498db; "
            "border-radius: 12px; "
            "width: 20px; "
            "height: 20px; "
            "background-color: transparent;"
        );
    } else {
        m_loadingIcon->setMovie(m_loadingMovie);
    }

    // 创建加载文本
    m_loadingText = new QLabel(DEFAULT_LOADING_TEXT, m_loadingWidget);
    m_loadingText->setAlignment(Qt::AlignCenter);
    m_loadingText->setStyleSheet("color: #666; font-size: 12px;");

    // 添加到布局
    m_loadingLayout->addWidget(m_loadingIcon);
    m_loadingLayout->addWidget(m_loadingText);

    updateLoadingIndicatorPosition();
}

void PullToRefreshListWidget::showLoadingIndicator()
{
    if (m_loadingWidget) {
        updateLoadingIndicatorPosition();
        m_loadingWidget->show();
        if (m_loadingMovie && m_loadingMovie->isValid()) {
            m_loadingMovie->start();
        } else {
            // 启动CSS旋转动画
            m_rotationTimer->start();
        }
    }
}

void PullToRefreshListWidget::hideLoadingIndicator()
{
    if (m_loadingWidget) {
        m_loadingWidget->hide();
        if (m_loadingMovie && m_loadingMovie->isValid()) {
            m_loadingMovie->stop();
        } else {
            // 停止CSS旋转动画
            m_rotationTimer->stop();
            m_rotationAngle = 0;
        }
    }
}

void PullToRefreshListWidget::updateLoadingIndicatorPosition()
{
    if (m_loadingWidget) {
        int x = (width() - m_loadingWidget->width()) / 2;
        // 将加载指示器放在底部
        int y = height() - m_pullDistance;
        m_loadingWidget->move(x, y);
        m_loadingWidget->resize(width(), m_loadingWidget->height());
    }
}

void PullToRefreshListWidget::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_startPos = event->pos();
        m_isDragging = false;
        m_scrollTimer->start();
    }
    QListWidget::mousePressEvent(event);
}

void PullToRefreshListWidget::mouseMoveEvent(QMouseEvent *event)
{
    if (event->buttons() & Qt::LeftButton) {
        QPoint diff = event->pos() - m_startPos;

        // 检查是否在列表底部且向上拖拽
        QScrollBar *scrollBar = verticalScrollBar();
        bool atBottom = (scrollBar->value() >= scrollBar->maximum());

        if (atBottom && diff.y() < 0) { // 向上拖拽（diff.y() < 0）
            m_isDragging = true;
            m_pullDistance = qMin(qAbs(diff.y()), m_refreshThreshold * 2);

            if (m_pullDistance > 10) { // 最小拖拽距离
                showLoadingIndicator();
                updateLoadingIndicatorPosition();
            }

            // 阻止默认滚动行为
            return;
        }
    }
    QListWidget::mouseMoveEvent(event);
}

void PullToRefreshListWidget::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_scrollTimer->stop();

        if (m_isDragging && m_pullDistance >= m_refreshThreshold && !m_isRefreshing) {
            // 触发刷新
            m_isRefreshing = true;
            showLoadingIndicator();

            // 调用虚函数
            onRefreshRequested();
        } else {
            // 没有达到刷新阈值，隐藏指示器
            hideLoadingIndicator();
            m_pullDistance = 0;
        }

        m_isDragging = false;
    }
    QListWidget::mouseReleaseEvent(event);
}

void PullToRefreshListWidget::wheelEvent(QWheelEvent *event)
{
    // 如果正在刷新，阻止滚轮事件
    if (m_isRefreshing) {
        return;
    }
    QListWidget::wheelEvent(event);
}

void PullToRefreshListWidget::checkScrollPosition()
{
    // 检查是否在底部，如果不在底部且正在拖拽，则停止拖拽
    QScrollBar *scrollBar = verticalScrollBar();
    bool atBottom = (scrollBar->value() >= scrollBar->maximum());

    if (m_isDragging && !atBottom) {
        m_isDragging = false;
        hideLoadingIndicator();
        m_pullDistance = 0;
    }
}

void PullToRefreshListWidget::rotateLoadingIcon()
{
    if (m_loadingIcon && !m_loadingMovie->isValid()) {
        m_rotationAngle += 10; // 每次旋转10度
        if (m_rotationAngle >= 360) {
            m_rotationAngle = 0;
        }

        // 使用CSS transform来旋转
        QString styleSheet = QString(
            "border: 2px solid #f3f3f3; "
            "border-top: 2px solid #3498db; "
            "border-radius: 12px; "
            "width: 20px; "
            "height: 20px; "
            "background-color: transparent;"
        );

        m_loadingIcon->setStyleSheet(styleSheet);
    }
}
