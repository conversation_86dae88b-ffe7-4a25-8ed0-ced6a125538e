#include "pulltorefreshlistwidget.h"
#include <QApplication>
#include <QStyle>
#include <QDebug>
#include <QScroller>
#include <QScrollerProperties>
#include <QGestureEvent>
#include <QPanGesture>
#include <QEvent>
#include <QTouchEvent>
#include <QScroller>
#include <QtMath>

const QString PullToRefreshListWidget::DEFAULT_LOADING_TEXT = "正在加载...";

PullToRefreshListWidget::PullToRefreshListWidget(QWidget *parent)
    : QListWidget(parent)
    , m_loadingWidget(nullptr)
    , m_loadingIcon(nullptr)
    , m_loadingText(nullptr)
    , m_loadingMovie(nullptr)
    , m_loadingLayout(nullptr)
    , m_isDragging(false)
    , m_isRefreshing(false)
    , m_refreshThreshold(DEFAULT_REFRESH_THRESHOLD)
    , m_pullDistance(0)
    , m_scrollTimer(new QTimer(this))
    , m_rotationTimer(new QTimer(this))
    , m_rotationAngle(0)
    , m_scroller(nullptr)
{
    setupLoadingIndicator();

    // 设置定时器检查滚动位置
    m_scrollTimer->setInterval(50); // 50ms检查一次
    connect(m_scrollTimer, &QTimer::timeout, this, &PullToRefreshListWidget::checkScrollPosition);

    // 设置旋转动画定时器
    m_rotationTimer->setInterval(50); // 50ms旋转一次
    connect(m_rotationTimer, &QTimer::timeout, this, &PullToRefreshListWidget::rotateLoadingIcon);

    // 设置Android平板触摸滑动
    setVerticalScrollMode(QListWidget::ScrollPerPixel);

    // 首先启用手势识别
    setAttribute(Qt::WA_AcceptTouchEvents, true);
    grabGesture(Qt::PanGesture);

    // 然后配置QScroller
    m_scroller = QScroller::scroller(this);

    // 配置QScroller属性，优化Android平板体验
    QScrollerProperties properties = m_scroller->scrollerProperties();
    properties.setScrollMetric(QScrollerProperties::DragStartDistance, 0.01);
    properties.setScrollMetric(QScrollerProperties::DragVelocitySmoothingFactor, 0.8);
    properties.setScrollMetric(QScrollerProperties::MinimumVelocity, 0.0);
    properties.setScrollMetric(QScrollerProperties::MaximumVelocity, 2.0);
    properties.setScrollMetric(QScrollerProperties::AcceleratingFlickMaximumTime, 0.5);
    properties.setScrollMetric(QScrollerProperties::AcceleratingFlickSpeedupFactor, 1.2);
    m_scroller->setScrollerProperties(properties);

    // 启用触摸手势（在手势注册之后）
    m_scroller->grabGesture(this, QScroller::TouchGesture);

    qDebug() << "Android平板手势初始化完成";
}

PullToRefreshListWidget::~PullToRefreshListWidget()
{
    if (m_loadingMovie) {
        delete m_loadingMovie;
    }
}

void PullToRefreshListWidget::setRefreshThreshold(int threshold)
{
    m_refreshThreshold = threshold;
}

void PullToRefreshListWidget::setLoadingText(const QString &text)
{
    if (m_loadingText) {
        m_loadingText->setText(text);
    }
}

void PullToRefreshListWidget::finishRefresh()
{
    m_isRefreshing = false;
    hideLoadingIndicator();
    m_pullDistance = 0;
}

void PullToRefreshListWidget::setupLoadingIndicator()
{
    // 创建加载指示器容器
    m_loadingWidget = new QWidget(this);
    m_loadingWidget->setFixedHeight(60);
    m_loadingWidget->hide();

    // 创建布局
    m_loadingLayout = new QVBoxLayout(m_loadingWidget);
    m_loadingLayout->setAlignment(Qt::AlignCenter);
    m_loadingLayout->setContentsMargins(0, 10, 0, 10);

    // 创建加载图标
    m_loadingIcon = new QLabel(m_loadingWidget);
    m_loadingIcon->setAlignment(Qt::AlignCenter);
    m_loadingIcon->setFixedSize(24, 24);

    // 创建旋转动画
    m_loadingMovie = new QMovie(":/icons/loading.gif");
    if (!m_loadingMovie->isValid()) {
        // 如果没有gif资源，创建一个CSS样式的旋转指示器
        m_loadingIcon->setText("");
        m_loadingIcon->setStyleSheet(
            "border: 2px solid #f3f3f3; "
            "border-top: 2px solid #3498db; "
            "border-radius: 12px; "
            "width: 20px; "
            "height: 20px; "
            "background-color: transparent;"
        );
    } else {
        m_loadingIcon->setMovie(m_loadingMovie);
    }

    // 创建加载文本
    m_loadingText = new QLabel(DEFAULT_LOADING_TEXT, m_loadingWidget);
    m_loadingText->setAlignment(Qt::AlignCenter);
    m_loadingText->setStyleSheet("color: #666; font-size: 12px;");

    // 添加到布局
    m_loadingLayout->addWidget(m_loadingIcon);
    m_loadingLayout->addWidget(m_loadingText);

    updateLoadingIndicatorPosition();
}

void PullToRefreshListWidget::showLoadingIndicator()
{
    if (m_loadingWidget) {
        updateLoadingIndicatorPosition();
        m_loadingWidget->show();
        if (m_loadingMovie && m_loadingMovie->isValid()) {
            m_loadingMovie->start();
        } else {
            // 启动CSS旋转动画
            m_rotationTimer->start();
        }
    }
}

void PullToRefreshListWidget::hideLoadingIndicator()
{
    if (m_loadingWidget) {
        m_loadingWidget->hide();
        if (m_loadingMovie && m_loadingMovie->isValid()) {
            m_loadingMovie->stop();
        } else {
            // 停止CSS旋转动画
            m_rotationTimer->stop();
            m_rotationAngle = 0;
        }
    }
}

void PullToRefreshListWidget::updateLoadingIndicatorPosition()
{
    if (m_loadingWidget) {
        int x = (width() - m_loadingWidget->width()) / 2;
        // 将加载指示器放在底部
        int y = height() - m_pullDistance;
        m_loadingWidget->move(x, y);
        m_loadingWidget->resize(width(), m_loadingWidget->height());
    }
}

bool PullToRefreshListWidget::event(QEvent *event)
{
    // Android平板手势事件处理
    if (event->type() == QEvent::Gesture) {
        qDebug() << "Android接收到手势事件";
        return gestureEvent(static_cast<QGestureEvent*>(event));
    }

    // Android平板触摸事件处理
    if (event->type() == QEvent::TouchBegin ||
        event->type() == QEvent::TouchUpdate ||
        event->type() == QEvent::TouchEnd) {
        qDebug() << "Android接收到触摸事件:" << event->type();
        touchEvent(static_cast<QTouchEvent*>(event));
        return true;
    }

    return QListWidget::event(event);
}

bool PullToRefreshListWidget::gestureEvent(QGestureEvent *event)
{
    qDebug() << "gestureEvent被调用";

    if (QGesture *pan = event->gesture(Qt::PanGesture)) {
        QPanGesture *panGesture = static_cast<QPanGesture*>(pan);
        qDebug() << "检测到PanGesture，状态:" << panGesture->state();

        switch (panGesture->state()) {
        case Qt::GestureStarted: {
            // Android平板：检查是否在底部开始手势
            QScrollBar *scrollBar = verticalScrollBar();
            bool atBottom = (scrollBar->value() >= scrollBar->maximum());

            if (atBottom) {
                m_startPos = QPointF(0, 0);
                m_isDragging = false;
                m_pullDistance = 0;
                m_scrollTimer->start();
                qDebug() << "Android手势开始 - 在底部";
            }
            break;
        }
        case Qt::GestureUpdated: {
            // Android平板：检查是否在列表底部
            QScrollBar *scrollBar = verticalScrollBar();
            bool atBottom = (scrollBar->value() >= scrollBar->maximum());

            if (atBottom) {
                // 使用delta累积向上拖拽距离
                QPointF delta = panGesture->delta();

                // Android平板触摸：检测向上滑动，增加阈值避免误触发
                if (delta.y() < -2.0) { // 向上拖拽，增加敏感度阈值
                    m_pullDistance += qAbs(delta.y());
                    m_pullDistance = qMin((qreal)m_pullDistance, (qreal)(m_refreshThreshold * 2));
                    m_isDragging = true;

                    qDebug() << "Android向上滑动，累积距离:" << m_pullDistance;

                    if (m_pullDistance > 15) { // Android平板适配的最小距离
                        showLoadingIndicator();
                        updateLoadingIndicatorPosition();
                    }

                    event->accept();
                    return true;
                }
            }
            break;
        }

        case Qt::GestureFinished:
        case Qt::GestureCanceled:
            m_scrollTimer->stop();

            qDebug() << "Android手势结束 - 拖拽距离:" << m_pullDistance << "阈值:" << m_refreshThreshold;

            if (m_isDragging && m_pullDistance >= m_refreshThreshold && !m_isRefreshing) {
                // Android平板：触发加载更多
                m_isRefreshing = true;
                showLoadingIndicator();

                qDebug() << "Android触发加载更多";
                onRefreshRequested();
            } else {
                // 没有达到刷新阈值，隐藏指示器
                hideLoadingIndicator();
                m_pullDistance = 0;
            }

            m_isDragging = false;
            break;

        default:
            break;
        }
    }

    return true;
}

// Android平板触摸事件处理
void PullToRefreshListWidget::touchEvent(QTouchEvent *event)
{
    qDebug() << "Android触摸事件，类型:" << event->type();

    if (event->touchPoints().isEmpty()) {
        return;
    }

    QTouchEvent::TouchPoint touchPoint = event->touchPoints().first();
    QScrollBar *scrollBar = verticalScrollBar();
    bool atBottom = (scrollBar->value() >= scrollBar->maximum());

    switch (event->type()) {
    case QEvent::TouchBegin:
        if (atBottom) {
            m_startPos = touchPoint.pos();
            m_isDragging = false;
            m_pullDistance = 0;
            m_scrollTimer->start();
            qDebug() << "Android触摸开始 - 在底部";
        }
        break;

    case QEvent::TouchUpdate:
        if (atBottom) {
            QPointF diff = touchPoint.pos() - m_startPos;

            if (diff.y() < -15.0) { // Android平板向上滑动阈值
                m_isDragging = true;
                m_pullDistance = qMin(qAbs(diff.y()), (qreal)(m_refreshThreshold * 2));

                qDebug() << "Android触摸拖拽 - 距离:" << m_pullDistance;

                if (m_pullDistance > 20) { // Android平板最小距离
                    showLoadingIndicator();
                    updateLoadingIndicatorPosition();
                }
            }
        }
        break;

    case QEvent::TouchEnd:
        m_scrollTimer->stop();

        qDebug() << "Android触摸结束 - 拖拽距离:" << m_pullDistance;

        if (m_isDragging && m_pullDistance >= m_refreshThreshold && !m_isRefreshing) {
            qDebug() << "Android触摸触发加载更多";
            m_isRefreshing = true;
            showLoadingIndicator();
            onRefreshRequested();
        } else {
            hideLoadingIndicator();
            m_pullDistance = 0;
        }

        m_isDragging = false;
        break;

    default:
        break;
    }
}



void PullToRefreshListWidget::checkScrollPosition()
{
    // 检查是否在底部，如果不在底部且正在拖拽，则停止拖拽
    QScrollBar *scrollBar = verticalScrollBar();
    bool atBottom = (scrollBar->value() >= scrollBar->maximum());

    if (m_isDragging && !atBottom) {
        m_isDragging = false;
        hideLoadingIndicator();
        m_pullDistance = 0;
    }
}

void PullToRefreshListWidget::rotateLoadingIcon()
{
    if (m_loadingIcon && !m_loadingMovie->isValid()) {
        m_rotationAngle += 10; // 每次旋转10度
        if (m_rotationAngle >= 360) {
            m_rotationAngle = 0;
        }

        // 使用CSS transform来旋转
        QString styleSheet = QString(
            "border: 2px solid #f3f3f3; "
            "border-top: 2px solid #3498db; "
            "border-radius: 12px; "
            "width: 20px; "
            "height: 20px; "
            "background-color: transparent;"
        );

        m_loadingIcon->setStyleSheet(styleSheet);
    }
}
