/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 5.13.2
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

static const unsigned char qt_resource_data[] = {
  // F:/code/M2/test/test/icons/loading.gif
  0x0,0x0,0x6,0x2d,
  0x47,
  0x49,0x46,0x38,0x39,0x61,0x18,0x0,0x18,0x0,0x80,0x0,0x0,0x0,0x0,0x0,0x0,
  0x0,0x0,0x21,0xff,0xb,0x4e,0x45,0x54,0x53,0x43,0x41,0x50,0x45,0x32,0x2e,0x30,
  0x3,0x1,0x0,0x0,0x0,0x21,0xfe,0x7,0x67,0x69,0x66,0x2e,0x73,0x6b,0x69,0x0,
  0x21,0xf9,0x4,0x9,0xa,0x0,0x0,0x0,0x2c,0x0,0x0,0x0,0x0,0x18,0x0,0x18,
  0x0,0x80,0x47,0x50,0x4c,0x95,0x95,0x93,0x2,0x41,0x84,0x8f,0xa9,0xcb,0x10,0xdd,
  0x9e,0x9,0x54,0xc2,0x53,0x67,0x76,0xd7,0xe5,0xbf,0x2d,0x4f,0x48,0x79,0x21,0x72,
  0x26,0x55,0xa9,0xa6,0xad,0xd5,0x76,0x93,0xc2,0xca,0xb0,0x29,0x33,0x2e,0x74,0xe7,
  0xd8,0xeb,0xa3,0xd5,0x3a,0x43,0x4e,0x50,0xd4,0xb,0x16,0x75,0xba,0x24,0x30,0x92,
  0x5c,0x22,0x3d,0x33,0x29,0x94,0x7a,0xcc,0x6a,0xd,0x5,0x0,0x21,0xf9,0x4,0x9,
  0xa,0x0,0x0,0x0,0x2c,0x2,0x0,0x2,0x0,0x15,0x0,0x15,0x0,0x80,0x47,0x50,
  0x4c,0x95,0x95,0x93,0x2,0x39,0x84,0x6f,0xa1,0x17,0xe8,0xb0,0x60,0x54,0xed,0xa5,
  0x45,0x65,0xb6,0xbb,0x6a,0x2e,0x59,0x90,0x56,0x6d,0xe2,0x35,0x45,0x27,0x59,0xae,
  0x4c,0xa6,0xba,0xe1,0x27,0x33,0x49,0xfd,0xe2,0xa2,0x29,0x63,0x71,0xaf,0x3b,0xfd,
  0x76,0x9e,0x59,0x10,0x31,0x34,0x72,0x8a,0xa3,0xe4,0xc3,0x77,0xb4,0x59,0xa,0x0,
  0x21,0xf9,0x4,0x9,0xa,0x0,0x0,0x0,0x2c,0x2,0x0,0x2,0x0,0x15,0x0,0x14,
  0x0,0x80,0x47,0x50,0x4c,0x95,0x95,0x93,0x2,0x39,0x84,0xf,0x11,0xa6,0xe8,0xae,
  0x56,0x5c,0xef,0xb4,0x98,0x30,0x46,0x33,0x59,0xba,0x71,0x4d,0x35,0x51,0x99,0x59,
  0x9d,0x1f,0x9a,0x5a,0x5f,0xb,0x85,0xb0,0xd8,0xcd,0x6b,0x6d,0x77,0xac,0xcd,0x93,
  0x6f,0x2f,0x2,0xee,0x78,0xa3,0x5f,0x6b,0x58,0x24,0xd,0x19,0xac,0x65,0x2c,0xc3,
  0x48,0x15,0x0,0x0,0x21,0xf9,0x4,0x9,0xa,0x0,0x0,0x0,0x2c,0x2,0x0,0x1,
  0x0,0x15,0x0,0x15,0x0,0x80,0x47,0x50,0x4c,0x95,0x95,0x93,0x2,0x39,0x84,0x8f,
  0x81,0xcb,0x10,0xfa,0x5e,0x63,0xb1,0x42,0x49,0xaf,0x91,0x35,0xe3,0xd5,0x65,0x93,
  0xa3,0x38,0xc7,0x37,0x6e,0x18,0xda,0x5c,0x11,0x99,0x9e,0x6f,0x2c,0xc2,0x74,0x7d,
  0x87,0xb7,0xca,0xd2,0x5d,0x3f,0x99,0x1,0x77,0xa5,0x58,0x51,0xb5,0xb,0x1e,0x13,
  0x29,0x4e,0x69,0x69,0x84,0x2e,0xa,0x0,0x21,0xf9,0x4,0x9,0xa,0x0,0x0,0x0,
  0x2c,0x2,0x0,0x1,0x0,0x15,0x0,0x15,0x0,0x80,0x47,0x50,0x4c,0x95,0x95,0x93,
  0x2,0x3a,0x84,0x7f,0x81,0xcb,0xa6,0x1,0x9d,0x6a,0x9,0xda,0x4b,0xeb,0xdb,0x93,
  0x59,0x3a,0x7d,0x5a,0x6,0x60,0xa4,0x27,0x8a,0x67,0x24,0x75,0x67,0xe9,0xbe,0x6d,
  0xfc,0x5e,0xac,0x6c,0xd3,0xe4,0x2d,0x4b,0x15,0x9e,0xd2,0x2d,0x6c,0x8,0x61,0x51,
  0xc5,0xf3,0xc0,0x74,0x49,0x5f,0x86,0x15,0x31,0x82,0x4e,0x5,0x0,0x21,0xf9,0x4,
  0x9,0xa,0x0,0x0,0x0,0x2c,0x2,0x0,0x2,0x0,0x15,0x0,0x14,0x0,0x80,0x47,
  0x50,0x4c,0x95,0x95,0x93,0x2,0x37,0x84,0xf,0xa1,0xb9,0xe8,0xac,0x56,0x4c,0xef,
  0x4c,0x1b,0xe0,0x93,0x15,0x66,0xd4,0x74,0xcc,0x26,0x3a,0x57,0x69,0x4e,0x21,0x1a,
  0x65,0x2b,0xfb,0xa2,0xdb,0x27,0x6b,0xb1,0xd8,0xd6,0x98,0x4a,0xc3,0xdf,0x9d,0xd2,
  0x81,0x4e,0x18,0x1c,0xb1,0x96,0xeb,0x48,0x5c,0x6,0xa0,0x9,0x55,0x0,0x0,0x21,
  0xf9,0x4,0x9,0xa,0x0,0x0,0x0,0x2c,0x2,0x0,0x1,0x0,0x15,0x0,0x15,0x0,
  0x80,0x47,0x50,0x4c,0x95,0x95,0x93,0x2,0x39,0x84,0x8f,0x10,0xc9,0x19,0xfb,0x56,
  0x43,0x4f,0xc1,0x58,0x67,0x76,0x9b,0x75,0x7a,0x51,0x13,0x27,0x59,0x23,0x58,0x9e,
  0x9e,0xf4,0xa9,0x55,0x3b,0x62,0xea,0xfa,0xce,0xa0,0x69,0xe7,0x5a,0x9a,0xcb,0x6,
  0xbf,0x83,0x35,0x5e,0x11,0xc7,0xb0,0xb8,0x53,0xa0,0x84,0x87,0x9a,0x4f,0x67,0xe1,
  0x15,0x0,0x0,0x21,0xf9,0x4,0x9,0xa,0x0,0x0,0x0,0x2c,0x2,0x0,0x1,0x0,
  0x15,0x0,0x16,0x0,0x80,0x47,0x50,0x4c,0x95,0x95,0x93,0x2,0x3c,0x84,0x8f,0x11,
  0xc8,0xad,0xfb,0xc0,0x6a,0x2c,0x2a,0x79,0x69,0x76,0x77,0x26,0x4f,0x61,0xde,0x16,
  0x1a,0x91,0x9,0x96,0x92,0xca,0x76,0xa9,0xfa,0x9c,0x2c,0xfa,0xce,0x76,0xe9,0xde,
  0x62,0xd,0xcb,0xb7,0x4c,0xd2,0xbc,0x82,0x15,0xdf,0x21,0x95,0x89,0x9,0x57,0x4a,
  0x65,0xc8,0x65,0x6c,0x45,0x67,0xc4,0x43,0x1,0x0,0x21,0xf9,0x4,0x9,0xa,0x0,
  0x0,0x0,0x2c,0x2,0x0,0x1,0x0,0x15,0x0,0x16,0x0,0x80,0x47,0x50,0x4c,0x95,
  0x95,0x93,0x2,0x3d,0x84,0x7f,0x11,0xc8,0xd,0xfb,0x96,0x82,0xce,0xcc,0x18,0x69,
  0x53,0x75,0x72,0xf6,0x75,0xa1,0x55,0x81,0xa0,0x56,0x66,0xa9,0x88,0xae,0x89,0xd4,
  0xa6,0xa3,0x6b,0x5d,0xf4,0xcb,0xc5,0xe5,0x37,0xef,0xba,0x3c,0xea,0x6d,0x6c,0x98,
  0x4e,0xad,0x95,0xe3,0x11,0x59,0x49,0x21,0xc2,0x3,0x73,0x3e,0x43,0x52,0xd3,0xaa,
  0x0,0x0,0x21,0xf9,0x4,0x9,0xa,0x0,0x0,0x0,0x2c,0x3,0x0,0x1,0x0,0x14,
  0x0,0x16,0x0,0x80,0x47,0x50,0x4c,0x95,0x95,0x93,0x2,0x39,0x84,0x7f,0x81,0xcb,
  0xa1,0x6e,0x20,0x43,0x52,0x1,0x39,0xf1,0x7a,0x96,0xce,0xa4,0x7d,0xcd,0x26,0x36,
  0x5d,0x49,0x85,0x68,0xc4,0xad,0xe9,0xb9,0x3a,0x70,0x2c,0xcf,0x65,0xeb,0xca,0xee,
  0xcb,0xe6,0x9a,0x6e,0xb2,0xd5,0x7a,0x17,0x20,0x48,0x37,0x1c,0x6e,0x90,0x40,0x55,
  0xd0,0x76,0x83,0x2,0xa,0x0,0x21,0xf9,0x4,0x9,0xa,0x0,0x0,0x0,0x2c,0x3,
  0x0,0x1,0x0,0x14,0x0,0x15,0x0,0x80,0x47,0x50,0x4c,0x95,0x95,0x93,0x2,0x38,
  0x84,0x7f,0x81,0xcb,0xa0,0xf1,0x60,0x43,0x32,0x29,0xd7,0xee,0x84,0x3a,0x4d,0xfb,
  0x85,0xce,0xd5,0x85,0x9c,0x51,0x9a,0xe2,0x17,0xad,0x56,0xe5,0x8e,0xe9,0xda,0xc6,
  0xd5,0x9c,0x9d,0x27,0x1d,0xed,0xdb,0xcd,0xc1,0xc9,0x34,0x3b,0x98,0x24,0x98,0x3b,
  0xea,0x70,0x48,0xa4,0xc8,0x38,0x2b,0x0,0x0,0x21,0xf9,0x4,0x9,0xa,0x0,0x0,
  0x0,0x2c,0x2,0x0,0x2,0x0,0x15,0x0,0x14,0x0,0x80,0x47,0x50,0x4c,0x95,0x95,
  0x93,0x2,0x39,0x84,0x1f,0x16,0xb9,0xe7,0xf,0x9a,0x63,0x9,0x22,0x1b,0xeb,0x93,
  0xf8,0xee,0x8e,0x70,0x1a,0x98,0x4d,0xe4,0x89,0x2e,0x14,0xaa,0x34,0x23,0xeb,0xb1,
  0x14,0x47,0xaa,0xf6,0x8b,0xdd,0xea,0x29,0xd2,0xf9,0x3e,0xf1,0x6d,0x70,0x36,0x45,
  0x4b,0x78,0x9c,0x65,0x90,0xad,0x63,0x33,0xc5,0x4,0x14,0x0,0x0,0x21,0xf9,0x4,
  0x9,0xa,0x0,0x0,0x0,0x2c,0x2,0x0,0x2,0x0,0x16,0x0,0x14,0x0,0x80,0x47,
  0x50,0x4c,0x95,0x95,0x93,0x2,0x3c,0x84,0x6f,0x91,0xaa,0xe8,0x18,0x9a,0x6c,0x6f,
  0xd5,0x13,0x2f,0xa0,0x35,0x3b,0xaf,0x85,0x9c,0x38,0x86,0x1f,0x5,0x9a,0x9d,0xa,
  0x4d,0x2c,0x16,0xc5,0xa5,0x96,0xc9,0xf3,0xba,0xc5,0xaf,0x9e,0xdf,0xf,0xf,0x33,
  0x1,0x17,0xa9,0x53,0x49,0x96,0x8,0xe,0x89,0x4c,0xa4,0x8f,0xc8,0x58,0xd2,0x3c,
  0x45,0x43,0x1,0x0,0x21,0xf9,0x4,0x9,0xa,0x0,0x0,0x0,0x2c,0x2,0x0,0x2,
  0x0,0x15,0x0,0x14,0x0,0x80,0x47,0x50,0x4c,0x95,0x95,0x93,0x2,0x39,0x84,0x6f,
  0x91,0x8b,0x7d,0xa1,0x10,0x74,0x6f,0x52,0xe6,0xe2,0x5,0xba,0x6e,0xd9,0x75,0x9f,
  0x65,0x7d,0x12,0x7,0x89,0x26,0x9a,0xae,0x49,0x5a,0xae,0x53,0xec,0xd6,0xd9,0x4b,
  0x5f,0x2d,0x5c,0xcf,0xad,0x19,0x23,0x6d,0x7e,0xd,0x62,0xc5,0x78,0x54,0xf8,0x72,
  0x47,0x96,0x52,0x35,0x64,0x2,0xa,0x0,0x21,0xf9,0x4,0x9,0xa,0x0,0x0,0x0,
  0x2c,0x2,0x0,0x2,0x0,0x15,0x0,0x14,0x0,0x80,0x47,0x50,0x4c,0x95,0x95,0x93,
  0x2,0x39,0x84,0x6f,0x1,0x9a,0xe8,0x18,0x14,0x8b,0xcf,0xb1,0xba,0xae,0xc5,0x49,
  0x47,0xca,0x1d,0xe0,0xa7,0x71,0xe3,0x17,0x56,0x64,0x2a,0x96,0x6c,0x97,0xb9,0x29,
  0x29,0x9b,0xeb,0x4b,0xa1,0x6f,0xb6,0x87,0xba,0xed,0xf9,0xa9,0x40,0x9d,0xdc,0x83,
  0xb8,0x89,0xc5,0x84,0x43,0x1a,0xf3,0x98,0x43,0x1e,0xa,0x0,0x21,0xf9,0x4,0x9,
  0xa,0x0,0x0,0x0,0x2c,0x2,0x0,0x2,0x0,0x15,0x0,0x14,0x0,0x80,0x47,0x50,
  0x4c,0x95,0x95,0x93,0x2,0x39,0x84,0x6f,0x1,0x9a,0xe8,0xab,0x42,0x9c,0xef,0x48,
  0x59,0xdb,0xbb,0xac,0x62,0xc7,0x65,0x22,0x34,0x26,0xcc,0x55,0x5a,0xd3,0x97,0xaa,
  0x6c,0x8a,0xa1,0x2d,0xb9,0xcc,0x5b,0x7,0xaf,0xf8,0x18,0xcb,0xb6,0xcf,0xdb,0xd1,
  0x44,0x40,0x88,0xd0,0x94,0x41,0x7d,0x56,0x44,0x23,0xa7,0x78,0x63,0x3e,0xa,0x0,
  0x21,0xf9,0x4,0x9,0xa,0x0,0x0,0x0,0x2c,0x2,0x0,0x2,0x0,0x15,0x0,0x14,
  0x0,0x80,0x47,0x50,0x4c,0x95,0x95,0x93,0x2,0x39,0x84,0x1f,0x79,0xcb,0x9c,0xf,
  0x42,0x94,0x6d,0x3d,0x85,0x62,0xa5,0xb0,0x51,0x8b,0x55,0xe2,0xf5,0x89,0x13,0x12,
  0x9a,0x9a,0xa5,0x9a,0x65,0x3b,0x5d,0x30,0x2a,0xcf,0xe0,0xab,0x62,0x78,0xce,0xa5,
  0x76,0xd7,0xa,0x1,0x79,0xe,0x1f,0x6b,0x3,0x79,0xd,0x41,0xb1,0xe6,0xae,0x28,
  0x31,0x2,0xa,0x0,0x21,0xf9,0x4,0x9,0xa,0x0,0x0,0x0,0x2c,0x0,0x0,0x0,
  0x0,0x18,0x0,0x18,0x0,0x80,0x47,0x50,0x4c,0x95,0x95,0x93,0x2,0x3c,0x84,0x8f,
  0xa9,0xbb,0x11,0xc,0x91,0x73,0xb1,0x99,0xf9,0x68,0xc5,0x80,0xf7,0xf,0x79,0xda,
  0xf1,0x84,0xd3,0x16,0x95,0xa9,0x72,0x56,0xe6,0x88,0x4a,0xf0,0xa6,0x5e,0x6e,0x7c,
  0x37,0x73,0x5a,0xb7,0x79,0xbf,0xcb,0x49,0x6e,0x41,0x9f,0x49,0x47,0xa3,0xd5,0x64,
  0xca,0x93,0x31,0xe6,0x5c,0xfe,0x84,0x54,0x44,0x1,0x0,0x3b,
  
};

static const unsigned char qt_resource_name[] = {
  // icons
  0x0,0x5,
  0x0,0x6f,0xa6,0x53,
  0x0,0x69,
  0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x73,
    // loading.gif
  0x0,0xb,
  0x0,0xb0,0xe2,0x96,
  0x0,0x6c,
  0x0,0x6f,0x0,0x61,0x0,0x64,0x0,0x69,0x0,0x6e,0x0,0x67,0x0,0x2e,0x0,0x67,0x0,0x69,0x0,0x66,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/icons
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x2,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/icons/loading.gif
  0x0,0x0,0x0,0x10,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x97,0x8b,0x6a,0x58,0xd8,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#ifdef QT_NAMESPACE
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)(); }
   } dummy;
}
