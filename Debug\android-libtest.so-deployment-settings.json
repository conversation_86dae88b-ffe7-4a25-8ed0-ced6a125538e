{"description": "This file is generated by qmake to be read by androiddeployqt and should not be modified by hand.", "qt": "G:/Qt/Qt5.13.2/5.13.2/android_arm64_v8a", "sdk": "G:/android/android-sdk-windows", "sdkBuildToolsRevision": "35.0.1", "ndk": "G:\\android\\android-ndk-r20b", "toolchain-prefix": "llvm", "tool-prefix": "llvm", "toolchain-version": "4.9", "ndk-host": "windows-x86_64", "target-architecture": "arm64-v8a", "qml-root-path": "F:/code/M2/test/test", "stdcpp-path": "G:\\android\\android-ndk-r20b/sources/cxx-stl/llvm-libc++/libs/arm64-v8a/libc++_shared.so", "useLLVM": true, "application-binary": "F:/code/M2/test/test/Debug/libtest.so"}