QMAKE_CXX.QT_COMPILER_STDCXX = 201402L
QMAKE_CXX.QMAKE_CLANG_MAJOR_VERSION = 8
QMAKE_CXX.QMAKE_CLANG_MINOR_VERSION = 0
QMAKE_CXX.QMAKE_CLANG_PATCH_VERSION = 7
QMAKE_CXX.QMAKE_GCC_MAJOR_VERSION = 4
QMAKE_CXX.QMAKE_GCC_MINOR_VERSION = 2
QMAKE_CXX.QMAKE_GCC_PATCH_VERSION = 1
QMAKE_CXX.COMPILER_MACROS = \
    QT_COMPILER_STDCXX \
    QMAKE_CLANG_MAJOR_VERSION \
    QMAKE_CLANG_MINOR_VERSION \
    QMAKE_CLANG_PATCH_VERSION \
    QMAKE_GCC_MAJOR_VERSION \
    QMAKE_GCC_MINOR_VERSION \
    QMAKE_GCC_PATCH_VERSION
QMAKE_CXX.INCDIRS = \
    G:/android/android-ndk-r20b/sysroot/usr/include/aarch64-linux-android \
    G:/android/android-ndk-r20b/sources/cxx-stl/llvm-libc++/include \
    G:/android/android-ndk-r20b/sources/android/support/include \
    G:/android/android-ndk-r20b/sources/cxx-stl/llvm-libc++abi/include \
    G:/android/android-ndk-r20b/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/8.0.7/include \
    G:/android/android-ndk-r20b/sysroot/usr/include
QMAKE_CXX.LIBDIRS = \
    G:/android/android-ndk-r20b/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/8.0.7 \
    G:/android/android-ndk-r20b/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/8.0.7/lib/linux/aarch64 \
    G:/android/android-ndk-r20b/toolchains/aarch64-linux-android-4.9/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x \
    G:/android/android-ndk-r20b/toolchains/aarch64-linux-android-4.9/prebuilt/windows-x86_64/aarch64-linux-android/lib64 \
    G:/android/android-ndk-r20b/toolchains/aarch64-linux-android-4.9/prebuilt/windows-x86_64/aarch64-linux-android/lib \
    G:/android/android-ndk-r20b/platforms/android-21/arch-arm64/usr/lib
