#include "widget.h"
#include "ui_widget.h"
#include <QVBoxLayout>
#include <QTimer>
#include <QDebug>
#include <QApplication>
#include <QListWidgetItem>
#include <QPropertyAnimation>
#include <QEasingCurve>

// PullRefreshListWidget 实现
PullRefreshListWidget::PullRefreshListWidget(QWidget *parent)
    : QListWidget(parent)
    , m_refreshLabel(nullptr)
    , m_loadMoreLabel(nullptr)
    , m_refreshMovie(nullptr)
    , m_loadMoreMovie(nullptr)
    , m_hideTimer(new QTimer(this))
    , m_refreshEnabled(true)
    , m_loadMoreEnabled(true)
    , m_isRefreshing(false)
    , m_isLoadingMore(false)
    , m_isPullingDown(false)
    , m_isPullingUp(false)
    , m_pullDistance(0)
{
    setupUI();

    // 启用触摸手势
    setAttribute(Qt::WA_AcceptTouchEvents, true);
    grabGesture(Qt::PanGesture);

    // 连接滚动条信号
    connect(verticalScrollBar(), &QScrollBar::valueChanged,
            this, &PullRefreshListWidget::onScrollValueChanged);

    // 设置隐藏定时器
    m_hideTimer->setSingleShot(true);
    m_hideTimer->setInterval(2000);
    connect(m_hideTimer, &QTimer::timeout, this, [this]() {
        hideRefreshIndicator();
        hideLoadMoreIndicator();
    });
}

PullRefreshListWidget::~PullRefreshListWidget()
{
    if (m_refreshMovie) {
        m_refreshMovie->deleteLater();
    }
    if (m_loadMoreMovie) {
        m_loadMoreMovie->deleteLater();
    }
}

void PullRefreshListWidget::setupUI()
{
    // 创建下拉刷新指示器
    m_refreshLabel = new QLabel(this);
    m_refreshLabel->setAlignment(Qt::AlignCenter);
    m_refreshLabel->setStyleSheet(
        "QLabel { "
        "background-color: rgba(240, 240, 240, 200); "
        "border: 1px solid #ccc; "
        "border-radius: 5px; "
        "padding: 10px; "
        "font-size: 14px; "
        "color: #666; "
        "}"
    );
    m_refreshLabel->setText("下拉刷新");
    m_refreshLabel->hide();

    // 创建上拉加载指示器
    m_loadMoreLabel = new QLabel(this);
    m_loadMoreLabel->setAlignment(Qt::AlignCenter);
    m_loadMoreLabel->setStyleSheet(
        "QLabel { "
        "background-color: rgba(240, 240, 240, 200); "
        "border: 1px solid #ccc; "
        "border-radius: 5px; "
        "padding: 10px; "
        "font-size: 14px; "
        "color: #666; "
        "}"
    );
    m_loadMoreLabel->setText("上拉加载更多");
    m_loadMoreLabel->hide();

    // 创建加载动画
    m_refreshMovie = new QMovie(":/icons/loading.gif", QByteArray(), this);
    m_loadMoreMovie = new QMovie(":/icons/loading.gif", QByteArray(), this);

    // 设置列表样式，优化触摸体验
    setStyleSheet(
        "QListWidget { "
        "border: none; "
        "background-color: white; "
        "}"
        "QListWidget::item { "
        "border-bottom: 1px solid #eee; "
        "padding: 15px; "
        "font-size: 16px; "
        "}"
        "QListWidget::item:selected { "
        "background-color: #e3f2fd; "
        "}"
    );

    // 设置滚动条样式，适合触摸
    setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
}

void PullRefreshListWidget::setRefreshEnabled(bool enabled)
{
    m_refreshEnabled = enabled;
}

void PullRefreshListWidget::setLoadMoreEnabled(bool enabled)
{
    m_loadMoreEnabled = enabled;
}

void PullRefreshListWidget::finishRefresh()
{
    m_isRefreshing = false;
    m_refreshMovie->stop();
    m_refreshLabel->setText("刷新完成");
    m_hideTimer->start();
}

void PullRefreshListWidget::finishLoadMore()
{
    m_isLoadingMore = false;
    m_loadMoreMovie->stop();
    m_loadMoreLabel->setText("加载完成");
    m_hideTimer->start();
}

bool PullRefreshListWidget::event(QEvent *event)
{
    if (event->type() == QEvent::TouchBegin ||
        event->type() == QEvent::TouchUpdate ||
        event->type() == QEvent::TouchEnd) {
        touchEvent(static_cast<QTouchEvent*>(event));
        return true;
    } else if (event->type() == QEvent::Gesture) {
        QGestureEvent *gestureEvent = static_cast<QGestureEvent*>(event);
        if (QPanGesture *pan = qobject_cast<QPanGesture*>(gestureEvent->gesture(Qt::PanGesture))) {
            panGestureEvent(pan);
            return true;
        }
    }
    return QListWidget::event(event);
}

void PullRefreshListWidget::touchEvent(QTouchEvent *event)
{
    if (event->touchPoints().isEmpty()) {
        return;
    }

    QTouchEvent::TouchPoint touchPoint = event->touchPoints().first();
    QPointF currentPos = touchPoint.pos();

    switch (event->type()) {
    case QEvent::TouchBegin:
        m_lastTouchPos = currentPos;
        m_pullDistance = 0;
        break;

    case QEvent::TouchUpdate: {
        qreal deltaY = currentPos.y() - m_lastTouchPos.y();

        // 检查是否在顶部下拉刷新
        if (verticalScrollBar()->value() == 0 && deltaY > 0 && m_refreshEnabled && !m_isRefreshing) {
            m_isPullingDown = true;
            m_pullDistance += deltaY;
            updateRefreshIndicator(m_pullDistance);

            if (m_pullDistance > REFRESH_THRESHOLD) {
                showRefreshIndicator();
            }
        }
        // 检查是否在底部上拉加载
        else if (verticalScrollBar()->value() == verticalScrollBar()->maximum() &&
                 deltaY < 0 && m_loadMoreEnabled && !m_isLoadingMore) {
            m_isPullingUp = true;
            m_pullDistance += qAbs(deltaY);

            if (m_pullDistance > LOAD_MORE_THRESHOLD) {
                showLoadMoreIndicator();
            }
        }

        m_lastTouchPos = currentPos;
        break;
    }

    case QEvent::TouchEnd:
        if (m_isPullingDown && m_pullDistance > REFRESH_THRESHOLD && !m_isRefreshing) {
            emit pullToRefresh();
        }
        if (m_isPullingUp && m_pullDistance > LOAD_MORE_THRESHOLD && !m_isLoadingMore) {
            emit pullToLoadMore();
        }

        m_isPullingDown = false;
        m_isPullingUp = false;
        m_pullDistance = 0;
        break;

    default:
        break;
    }
}

void PullRefreshListWidget::panGestureEvent(QPanGesture *gesture)
{
    QPointF delta = gesture->delta();

    // 在顶部下拉
    if (verticalScrollBar()->value() == 0 && delta.y() > 0 && m_refreshEnabled && !m_isRefreshing) {
        if (gesture->state() == Qt::GestureFinished && delta.y() > REFRESH_THRESHOLD) {
            emit pullToRefresh();
        }
    }
    // 在底部上拉
    else if (verticalScrollBar()->value() == verticalScrollBar()->maximum() &&
             delta.y() < 0 && m_loadMoreEnabled && !m_isLoadingMore) {
        if (gesture->state() == Qt::GestureFinished && qAbs(delta.y()) > LOAD_MORE_THRESHOLD) {
            emit pullToLoadMore();
        }
    }
}

void PullRefreshListWidget::scrollContentsBy(int dx, int dy)
{
    QListWidget::scrollContentsBy(dx, dy);
    updateLoadMoreIndicator();
}

void PullRefreshListWidget::onScrollValueChanged(int value)
{
    Q_UNUSED(value)
    updateLoadMoreIndicator();
}

void PullRefreshListWidget::showRefreshIndicator()
{
    if (m_isRefreshing) return;

    m_isRefreshing = true;
    m_refreshLabel->setText("正在刷新...");
    m_refreshLabel->setMovie(m_refreshMovie);
    m_refreshMovie->start();

    // 定位到顶部中央
    int x = (width() - m_refreshLabel->width()) / 2;
    m_refreshLabel->move(x, 10);
    m_refreshLabel->show();
}

void PullRefreshListWidget::showLoadMoreIndicator()
{
    if (m_isLoadingMore) return;

    m_isLoadingMore = true;
    m_loadMoreLabel->setText("正在加载...");
    m_loadMoreLabel->setMovie(m_loadMoreMovie);
    m_loadMoreMovie->start();

    // 定位到底部中央
    int x = (width() - m_loadMoreLabel->width()) / 2;
    int y = height() - m_loadMoreLabel->height() - 10;
    m_loadMoreLabel->move(x, y);
    m_loadMoreLabel->show();
}

void PullRefreshListWidget::updateRefreshIndicator(int offset)
{
    if (offset > 10) {
        int x = (width() - m_refreshLabel->width()) / 2;
        int y = qMin(offset - 50, 10);
        m_refreshLabel->move(x, y);

        if (offset > REFRESH_THRESHOLD) {
            m_refreshLabel->setText("释放刷新");
        } else {
            m_refreshLabel->setText("下拉刷新");
        }
        m_refreshLabel->show();
    } else {
        m_refreshLabel->hide();
    }
}

void PullRefreshListWidget::updateLoadMoreIndicator()
{
    // 当滚动到接近底部时显示加载提示
    if (verticalScrollBar()->maximum() > 0) {
        int remaining = verticalScrollBar()->maximum() - verticalScrollBar()->value();
        if (remaining < 50 && m_loadMoreEnabled && !m_isLoadingMore) {
            int x = (width() - m_loadMoreLabel->width()) / 2;
            int y = height() - m_loadMoreLabel->height() - 10;
            m_loadMoreLabel->move(x, y);
            m_loadMoreLabel->setText("上拉加载更多");
            m_loadMoreLabel->show();
        }
    }
}

void PullRefreshListWidget::hideRefreshIndicator()
{
    m_refreshLabel->hide();
}

void PullRefreshListWidget::hideLoadMoreIndicator()
{
    m_loadMoreLabel->hide();
}

// Widget 实现
Widget::Widget(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::Widget)
    , m_listWidget(nullptr)
    , m_dataTimer(new QTimer(this))
    , m_dataIndex(0)
{
    ui->setupUi(this);
    setupUI();

    // 设置窗口大小，适合手机屏幕
    resize(400, 700);

    // 设置数据模拟定时器
    m_dataTimer->setSingleShot(true);
    connect(m_dataTimer, &QTimer::timeout, this, &Widget::simulateDataRefresh);
}

Widget::~Widget()
{
    delete ui;
}

void Widget::setupUI()
{
    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0, 0, 0, 0);

    // 创建自定义列表控件
    m_listWidget = new PullRefreshListWidget(this);
    mainLayout->addWidget(m_listWidget);

    // 连接信号
    connect(m_listWidget, &PullRefreshListWidget::pullToRefresh,
            this, &Widget::onRefresh);
    connect(m_listWidget, &PullRefreshListWidget::pullToLoadMore,
            this, &Widget::onLoadMore);

    // 添加初始数据
    addSampleData(20);

    // 设置窗口样式
    setStyleSheet(
        "Widget { "
        "background-color: #f5f5f5; "
        "}"
    );
}

void Widget::onRefresh()
{
    qDebug() << "开始刷新数据...";

    // 模拟网络请求延迟
    m_dataTimer->disconnect();
    connect(m_dataTimer, &QTimer::timeout, this, &Widget::simulateDataRefresh);
    m_dataTimer->start(2000); // 2秒后完成刷新
}

void Widget::onLoadMore()
{
    qDebug() << "开始加载更多数据...";

    // 模拟网络请求延迟
    m_dataTimer->disconnect();
    connect(m_dataTimer, &QTimer::timeout, this, &Widget::simulateDataLoad);
    m_dataTimer->start(1500); // 1.5秒后完成加载
}

void Widget::simulateDataRefresh()
{
    // 清空现有数据
    m_listWidget->clear();
    m_dataIndex = 0;

    // 添加新数据
    addSampleData(20);

    // 完成刷新
    m_listWidget->finishRefresh();

    qDebug() << "刷新完成";
}

void Widget::simulateDataLoad()
{
    // 添加更多数据
    int currentCount = m_listWidget->count();
    addSampleData(10, currentCount);

    // 完成加载
    m_listWidget->finishLoadMore();

    qDebug() << "加载更多完成";
}

void Widget::addSampleData(int count, int startIndex)
{
    for (int i = 0; i < count; ++i) {
        QListWidgetItem *item = new QListWidgetItem();

        // 创建不同类型的示例数据
        QString text;
        int dataType = (startIndex + i) % 4;

        switch (dataType) {
        case 0:
            text = QString("📱 消息 %1: 这是一条重要的通知消息").arg(startIndex + i + 1);
            break;
        case 1:
            text = QString("📧 邮件 %1: 您有新的邮件需要查看").arg(startIndex + i + 1);
            break;
        case 2:
            text = QString("📅 提醒 %1: 今天有重要的会议安排").arg(startIndex + i + 1);
            break;
        case 3:
            text = QString("🔔 通知 %1: 系统更新已完成").arg(startIndex + i + 1);
            break;
        }

        item->setText(text);

        // 设置项目数据
        item->setData(Qt::UserRole, startIndex + i + 1);

        m_listWidget->addItem(item);
    }

    m_dataIndex = startIndex + count;
}

