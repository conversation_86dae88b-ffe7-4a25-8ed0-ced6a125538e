#include "widget.h"
#include "ui_widget.h"
#include <QVBoxLayout>
#include <QTimer>
#include <QDebug>

// MyRefreshListWidget 实现
MyRefreshListWidget::MyRefreshListWidget(QWidget *parent)
    : PullToRefreshListWidget(parent)
    , m_itemCount(0)
{
    // 初始化一些数据
    for (int i = 0; i < 10; ++i) {
        addItem(QString("初始项目 %1").arg(i + 1));
        m_itemCount++;
    }

    setLoadingText("正在加载更多数据...");
}

void MyRefreshListWidget::onRefreshRequested()
{
    qDebug() << "开始加载更多数据...";

    // 模拟网络请求延迟
    QTimer::singleShot(2000, this, &MyRefreshListWidget::simulateDataLoading);
}

void MyRefreshListWidget::simulateDataLoading()
{
    // 模拟加载更多数据（添加到底部）
    for (int i = 0; i < 3; ++i) {
        addItem(QString("更多数据 %1").arg(m_itemCount + i + 1));
    }
    m_itemCount += 3;

    qDebug() << "更多数据加载完成";

    // 完成刷新
    finishRefresh();
}

// Widget 实现
Widget::Widget(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::Widget)
    , m_listWidget(nullptr)
{
    ui->setupUi(this);

    // 创建自定义的下拉刷新列表
    m_listWidget = new MyRefreshListWidget(this);

    // 创建布局并添加列表
    QVBoxLayout *layout = new QVBoxLayout(this);
    layout->addWidget(m_listWidget);
    setLayout(layout);

    // 设置窗口标题和大小
    setWindowTitle("上滑加载更多列表示例");
    resize(300, 500);
}

Widget::~Widget()
{
    delete ui;
}

