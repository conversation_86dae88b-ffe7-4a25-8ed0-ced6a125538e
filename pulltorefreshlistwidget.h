#ifndef PULLTOREFRESHLISTWIDGET_H
#define PULLTOREFRESHLISTWIDGET_H

#include <QListWidget>
#include <QLabel>
#include <QMovie>
#include <QVBoxLayout>
#include <QScrollBar>
#include <QMouseEvent>
#include <QTimer>
#include <QScroller>
#include <QScrollerProperties>
#include <QGestureEvent>
#include <QPanGesture>

class PullToRefreshListWidget : public QListWidget
{
    Q_OBJECT

public:
    explicit PullToRefreshListWidget(QWidget *parent = nullptr);
    virtual ~PullToRefreshListWidget();

    // 设置刷新阈值（像素）
    void setRefreshThreshold(int threshold);

    // 设置加载文本
    void setLoadingText(const QString &text);

    // 完成刷新，隐藏加载指示器
    void finishRefresh();

protected:
    // 虚函数，用户需要重写此函数来实现刷新逻辑（滑到底部时触发）
    virtual void onRefreshRequested() = 0;

    // 重写事件处理函数
    bool event(QEvent *event) override;
    bool gestureEvent(QGestureEvent *event);
    void wheelEvent(QWheelEvent *event) override;

    // 触摸事件处理（作为手势的补充）
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;

private slots:
    void checkScrollPosition();
    void rotateLoadingIcon();

private:
    void setupLoadingIndicator();
    void showLoadingIndicator();
    void hideLoadingIndicator();
    void updateLoadingIndicatorPosition();

private:
    // 加载指示器相关
    QWidget *m_loadingWidget;
    QLabel *m_loadingIcon;
    QLabel *m_loadingText;
    QMovie *m_loadingMovie;
    QVBoxLayout *m_loadingLayout;

    // 手势拖拽相关
    QPointF m_startPos;  // 支持手势和鼠标事件
    bool m_isDragging;
    bool m_isRefreshing;
    int m_refreshThreshold;
    int m_pullDistance;  // 向上拖拽距离
    QScroller *m_scroller;

    // 定时器用于检查滚动位置和旋转动画
    QTimer *m_scrollTimer;
    QTimer *m_rotationTimer;
    int m_rotationAngle;

    // 默认设置
    static const int DEFAULT_REFRESH_THRESHOLD = 80;
    static const QString DEFAULT_LOADING_TEXT;
};

#endif // PULLTOREFRESHLISTWIDGET_H
