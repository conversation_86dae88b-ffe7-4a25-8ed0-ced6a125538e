#ifndef PULLTOREFRESHLISTWIDGET_H
#define PULLTOREFRESHLISTWIDGET_H

#include <QListWidget>
#include <QLabel>
#include <QMovie>
#include <QVBoxLayout>
#include <QScrollBar>
#include <QMouseEvent>
#include <QTimer>

class PullToRefreshListWidget : public QListWidget
{
    Q_OBJECT

public:
    explicit PullToRefreshListWidget(QWidget *parent = nullptr);
    virtual ~PullToRefreshListWidget();

    // 设置刷新阈值（像素）
    void setRefreshThreshold(int threshold);
    
    // 设置加载文本
    void setLoadingText(const QString &text);
    
    // 完成刷新，隐藏加载指示器
    void finishRefresh();

protected:
    // 虚函数，用户需要重写此函数来实现刷新逻辑
    virtual void onRefreshRequested() = 0;
    
    // 重写事件处理函数
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;

private slots:
    void checkScrollPosition();

private:
    void setupLoadingIndicator();
    void showLoadingIndicator();
    void hideLoadingIndicator();
    void updateLoadingIndicatorPosition();

private:
    // 加载指示器相关
    QWidget *m_loadingWidget;
    QLabel *m_loadingIcon;
    QLabel *m_loadingText;
    QMovie *m_loadingMovie;
    QVBoxLayout *m_loadingLayout;
    
    // 拖拽相关
    QPoint m_startPos;
    bool m_isDragging;
    bool m_isRefreshing;
    int m_refreshThreshold;
    int m_pullDistance;
    
    // 定时器用于检查滚动位置
    QTimer *m_scrollTimer;
    
    // 默认设置
    static const int DEFAULT_REFRESH_THRESHOLD = 80;
    static const QString DEFAULT_LOADING_TEXT;
};

#endif // PULLTOREFRESHLISTWIDGET_H
