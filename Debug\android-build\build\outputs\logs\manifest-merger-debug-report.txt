-- Merging decision tree log ---
manifest
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:2:1-94:12
INJECTED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:2:1-94:12
INJECTED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:2:1-94:12
INJECTED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:2:1-94:12
INJECTED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:2:1-94:12
	package
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:2:11-47
	android:versionName
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:2:107-132
		INJECTED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml
		INJECTED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml
	xmlns:android
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:2:48-106
	android:versionCode
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:2:133-156
		INJECTED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml
		INJECTED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml
	android:installLocation
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:2:157-187
uses-sdk
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:3:5-73
	android:targetSdkVersion
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:3:42-71
	android:minSdkVersion
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:3:15-41
uses-permission#android.permission.INTERNET
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:7:9-71
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:7:26-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:8:5-81
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:8:22-78
supports-screens
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:15:5-135
	android:largeScreens
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:15:23-50
	android:smallScreens
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:15:106-133
	android:normalScreens
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:15:51-79
	android:anyDensity
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:15:80-105
application
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:17:5-92:19
	android:label
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:17:117-137
	android:hardwareAccelerated
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:17:18-52
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:17:53-116
activity#org.qtproject.qt5.android.bindings.QtActivity
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:18:9-88:16
	android:screenOrientation
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:21:19-58
	android:label
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:20:19-39
	android:launchMode
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:22:19-49
	android:configChanges
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:18:19-188
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:19:19-79
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:23:13-26:29
action#android.intent.action.MAIN
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:24:17-68
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:24:25-66
category#android.intent.category.LAUNCHER
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:25:17-76
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:25:27-74
meta-data#android.app.lib_name
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:32:13-82
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:32:60-80
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:32:24-59
meta-data#android.app.qt_sources_resource_id
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:33:13-112
	android:resource
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:33:74-110
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:33:24-73
meta-data#android.app.repository
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:34:13-87
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:34:62-85
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:34:24-61
meta-data#android.app.qt_libs_resource_id
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:35:13-106
	android:resource
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:35:71-104
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:35:24-70
meta-data#android.app.bundled_libs_resource_id
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:36:13-116
	android:resource
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:36:76-114
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:36:24-75
meta-data#android.app.bundle_local_qt_libs
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:38:13-91
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:38:72-89
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:38:24-71
meta-data#android.app.bundled_in_lib_resource_id
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:39:13-120
	android:resource
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:39:78-118
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:39:24-77
meta-data#android.app.bundled_in_assets_resource_id
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:40:13-126
	android:resource
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:40:81-124
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:40:24-80
meta-data#android.app.use_local_qt_libs
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:42:13-88
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:42:69-86
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:42:24-68
meta-data#android.app.libs_prefix
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:43:13-100
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:43:63-98
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:43:24-62
meta-data#android.app.load_local_libs
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:44:13-129
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:44:67-127
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:44:24-66
meta-data#android.app.load_local_jars
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:45:13-102
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:45:67-100
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:45:24-66
meta-data#android.app.static_init_classes
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:46:13-89
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:46:71-87
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:46:24-70
meta-data#android.app.ministro_not_found_msg
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:50:13-122
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:50:24-70
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:50:71-120
meta-data#android.app.ministro_needed_msg
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:51:13-116
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:51:24-67
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:51:68-114
meta-data#android.app.fatal_error_msg
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:52:13-108
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:52:24-63
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:52:64-106
meta-data#android.app.unsupported_android_version
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:53:13-132
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:53:24-75
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:53:76-130
meta-data#android.app.background_running
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:72:13-93
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:72:70-91
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:72:24-69
meta-data#android.app.auto_screen_scale_factor
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:76:13-99
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:76:76-97
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:76:24-75
meta-data#android.app.extract_android_style
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:86:13-98
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:86:73-96
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:86:24-72
