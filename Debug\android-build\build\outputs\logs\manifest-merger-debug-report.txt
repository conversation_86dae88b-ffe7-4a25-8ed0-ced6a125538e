-- Merging decision tree log ---
manifest
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:2:1-90:12
INJECTED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:2:1-90:12
INJECTED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:2:1-90:12
INJECTED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:2:1-90:12
INJECTED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:2:1-90:12
	package
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:2:11-42
	android:versionName
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:2:102-127
		INJECTED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml
		INJECTED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml
	xmlns:android
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:2:43-101
	android:versionCode
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:2:128-151
		INJECTED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml
		INJECTED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml
	android:installLocation
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:2:152-182
uses-sdk
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:3:5-73
	android:targetSdkVersion
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:3:42-71
	android:minSdkVersion
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:3:15-41
uses-permission#android.permission.INTERNET
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:7:9-71
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:7:26-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:8:5-81
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:8:22-78
supports-screens
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:15:5-135
	android:largeScreens
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:15:23-50
	android:smallScreens
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:15:106-133
	android:normalScreens
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:15:51-79
	android:anyDensity
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:15:80-105
application
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:17:5-88:19
	android:label
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:17:117-137
	android:hardwareAccelerated
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:17:18-52
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:17:53-116
activity#org.qtproject.qt5.android.bindings.QtActivity
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:18:9-84:16
	android:screenOrientation
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:18:271-310
	android:label
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:18:250-270
	android:launchMode
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:18:311-341
	android:configChanges
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:18:19-188
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:18:189-249
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:19:13-22:29
action#android.intent.action.MAIN
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:20:17-68
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:20:25-66
category#android.intent.category.LAUNCHER
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:21:17-76
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:21:27-74
meta-data#android.app.lib_name
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:28:13-82
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:28:60-80
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:28:24-59
meta-data#android.app.qt_sources_resource_id
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:29:13-112
	android:resource
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:29:74-110
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:29:24-73
meta-data#android.app.repository
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:30:13-87
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:30:62-85
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:30:24-61
meta-data#android.app.qt_libs_resource_id
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:31:13-106
	android:resource
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:31:71-104
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:31:24-70
meta-data#android.app.bundled_libs_resource_id
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:32:13-116
	android:resource
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:32:76-114
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:32:24-75
meta-data#android.app.bundle_local_qt_libs
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:34:13-91
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:34:72-89
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:34:24-71
meta-data#android.app.bundled_in_lib_resource_id
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:35:13-120
	android:resource
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:35:78-118
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:35:24-77
meta-data#android.app.bundled_in_assets_resource_id
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:36:13-126
	android:resource
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:36:81-124
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:36:24-80
meta-data#android.app.use_local_qt_libs
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:38:13-88
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:38:69-86
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:38:24-68
meta-data#android.app.libs_prefix
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:39:13-100
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:39:63-98
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:39:24-62
meta-data#android.app.load_local_libs
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:40:13-129
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:40:67-127
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:40:24-66
meta-data#android.app.load_local_jars
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:41:13-102
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:41:67-100
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:41:24-66
meta-data#android.app.static_init_classes
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:42:13-89
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:42:71-87
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:42:24-70
meta-data#android.app.ministro_not_found_msg
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:46:13-122
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:46:24-70
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:46:71-120
meta-data#android.app.ministro_needed_msg
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:47:13-116
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:47:24-67
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:47:68-114
meta-data#android.app.fatal_error_msg
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:48:13-108
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:48:24-63
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:48:64-106
meta-data#android.app.unsupported_android_version
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:49:13-132
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:49:24-75
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:49:76-130
meta-data#android.app.background_running
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:68:13-93
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:68:70-91
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:68:24-69
meta-data#android.app.auto_screen_scale_factor
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:72:13-99
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:72:76-97
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:72:24-75
meta-data#android.app.extract_android_style
ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:82:13-98
	android:value
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:82:73-96
	android:name
		ADDED from F:\code\M2\test\test\Debug\android-build\AndroidManifest.xml:82:24-72
