#!/usr/bin/env python3
"""
生成一个简单的loading.gif文件
需要安装: pip install Pillow
"""

try:
    from PIL import Image, ImageDraw
    import math
    import os
except ImportError:
    print("请先安装Pillow库: pip install Pillow")
    exit(1)

def create_loading_gif():
    """创建一个旋转的loading.gif动画"""
    
    # 设置参数
    size = 24
    frames = 12  # 动画帧数
    duration = 100  # 每帧持续时间(毫秒)
    
    # 创建帧列表
    frames_list = []
    
    for i in range(frames):
        # 创建透明背景的图像
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # 计算旋转角度
        angle = (360 / frames) * i
        
        # 绘制圆环
        center = size // 2
        radius = 8
        thickness = 2
        
        # 绘制背景圆环（浅灰色）
        draw.ellipse([center - radius, center - radius, 
                     center + radius, center + radius], 
                    outline=(200, 200, 200, 100), width=thickness)
        
        # 绘制旋转的部分（蓝色）
        start_angle = angle
        end_angle = angle + 90
        
        # 由于PIL的arc方法限制，我们用多个点来模拟弧线
        for a in range(int(start_angle), int(end_angle), 5):
            rad = math.radians(a)
            x1 = center + (radius - thickness) * math.cos(rad)
            y1 = center + (radius - thickness) * math.sin(rad)
            x2 = center + (radius + thickness) * math.cos(rad)
            y2 = center + (radius + thickness) * math.sin(rad)
            
            draw.line([(x1, y1), (x2, y2)], fill=(52, 152, 219, 255), width=1)
        
        frames_list.append(img)
    
    # 保存为GIF
    output_path = os.path.join('icons', 'loading.gif')
    
    # 确保icons目录存在
    os.makedirs('icons', exist_ok=True)
    
    # 保存动画GIF
    frames_list[0].save(
        output_path,
        save_all=True,
        append_images=frames_list[1:],
        duration=duration,
        loop=0,  # 无限循环
        transparency=0,
        disposal=2
    )
    
    print(f"Loading.gif已生成: {output_path}")
    print(f"文件大小: {size}x{size} 像素")
    print(f"帧数: {frames}")
    print(f"动画时长: {duration}ms per frame")

def create_simple_spinner():
    """创建一个更简单的旋转器"""
    size = 24
    frames = 8
    duration = 125
    
    frames_list = []
    
    for i in range(frames):
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        center = size // 2
        angle = (360 / frames) * i
        
        # 绘制8个点，其中一个高亮
        for j in range(8):
            point_angle = (360 / 8) * j
            rad = math.radians(point_angle)
            
            x = center + 8 * math.cos(rad)
            y = center + 8 * math.sin(rad)
            
            # 当前旋转位置的点高亮
            if j == (i % 8):
                color = (52, 152, 219, 255)  # 蓝色
                radius = 2
            else:
                color = (200, 200, 200, 100)  # 浅灰色
                radius = 1
            
            draw.ellipse([x - radius, y - radius, x + radius, y + radius], 
                        fill=color)
        
        frames_list.append(img)
    
    output_path = os.path.join('icons', 'loading.gif')
    os.makedirs('icons', exist_ok=True)
    
    frames_list[0].save(
        output_path,
        save_all=True,
        append_images=frames_list[1:],
        duration=duration,
        loop=0,
        transparency=0,
        disposal=2
    )
    
    print(f"简单旋转器已生成: {output_path}")

if __name__ == "__main__":
    print("选择要生成的loading动画类型:")
    print("1. 圆环旋转器")
    print("2. 点旋转器")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        create_loading_gif()
    elif choice == "2":
        create_simple_spinner()
    else:
        print("无效选择，生成默认的点旋转器")
        create_simple_spinner()
