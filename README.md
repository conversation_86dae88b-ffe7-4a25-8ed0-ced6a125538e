# 上滑加载更多列表组件 (PullToRefreshListWidget)

这是一个继承自QListWidget的自定义组件，支持手势上滑到底部加载更多数据的功能。

## 功能特性

- 🔄 手势上滑到底部加载更多功能
- 🎯 可自定义刷新阈值
- 💫 加载动画指示器
- 📝 可自定义加载文本
- 🎨 简洁的API设计
- 📱 支持触摸手势和鼠标操作

## 使用方法

### 1. 继承PullToRefreshListWidget类

```cpp
class MyRefreshListWidget : public PullToRefreshListWidget
{
    Q_OBJECT

public:
    explicit MyRefreshListWidget(QWidget *parent = nullptr);

protected:
    // 重写此虚函数来实现你的加载更多逻辑
    void onRefreshRequested() override;

private slots:
    void simulateDataLoading();
};
```

### 2. 实现加载更多逻辑

```cpp
void MyRefreshListWidget::onRefreshRequested()
{
    // 在这里实现你的加载更多数据逻辑
    // 比如网络请求、数据库查询等

    // 模拟异步操作
    QTimer::singleShot(2000, this, &MyRefreshListWidget::simulateDataLoading);
}

void MyRefreshListWidget::simulateDataLoading()
{
    // 加载完成后添加更多数据到列表底部
    addItem("更多数据项");

    // 重要：完成加载后必须调用此方法
    finishRefresh();
}
```

### 3. 可选配置

```cpp
// 设置刷新阈值（默认80像素）
setRefreshThreshold(100);

// 设置加载文本
setLoadingText("正在加载更多数据...");
```

## 工作原理

1. **检测上滑手势**：使用QScroller和QPanGesture检测用户在列表底部的向上滑动手势
2. **显示加载指示器**：达到一定拖拽距离时，显示加载动画和文本
3. **触发加载**：当拖拽距离超过阈值并释放时，调用`onRefreshRequested()`虚函数
4. **完成加载**：用户在加载逻辑完成后调用`finishRefresh()`来隐藏加载指示器

## 技术实现

- **手势识别**：使用Qt的QScroller和QPanGesture实现流畅的手势检测
- **触摸支持**：原生支持触摸屏设备和鼠标操作
- **平滑滚动**：使用ScrollPerPixel模式提供更好的滚动体验

## 注意事项

- 必须重写`onRefreshRequested()`虚函数
- 加载完成后必须调用`finishRefresh()`
- 如果需要自定义加载动画，可以在`icons/`目录下放置`loading.gif`文件

## 生成loading.gif文件

### 方法1：使用Python脚本生成
```bash
# 安装依赖
pip install Pillow

# 运行生成脚本
python generate_loading_gif.py
```

### 方法2：在线生成器
- [Loading.io](https://loading.io/) - 免费的加载动画生成器
- [Icons8 Preloaders](https://icons8.com/preloaders/) - 各种预设的加载动画
- [CSSLoad.net](https://cssload.net/) - CSS和GIF加载动画

### 方法3：查看HTML示例
打开 `generate_loading_icon.html` 文件查看CSS动画效果和详细说明。

### 备选方案
如果没有提供loading.gif文件，组件会自动使用CSS样式的旋转动画作为备选方案。

## 文件结构

```
├── pulltorefreshlistwidget.h    # 头文件
├── pulltorefreshlistwidget.cpp  # 实现文件
├── resources.qrc                # 资源文件
├── icons/
│   └── loading.gif             # 加载动画（可选）
└── widget.cpp                  # 使用示例
```

## 编译说明

确保在.pro文件中包含了所有必要的文件：

```pro
SOURCES += pulltorefreshlistwidget.cpp
HEADERS += pulltorefreshlistwidget.h
RESOURCES += resources.qrc
```
