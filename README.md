# 下拉刷新列表组件 (PullToRefreshListWidget)

这是一个继承自QListWidget的自定义组件，支持下拉刷新功能。

## 功能特性

- 🔄 下拉刷新功能
- 🎯 可自定义刷新阈值
- 💫 加载动画指示器
- 📝 可自定义加载文本
- 🎨 简洁的API设计

## 使用方法

### 1. 继承PullToRefreshListWidget类

```cpp
class MyRefreshListWidget : public PullToRefreshListWidget
{
    Q_OBJECT

public:
    explicit MyRefreshListWidget(QWidget *parent = nullptr);

protected:
    // 重写此虚函数来实现你的刷新逻辑
    void onRefreshRequested() override;

private slots:
    void simulateDataLoading();
};
```

### 2. 实现刷新逻辑

```cpp
void MyRefreshListWidget::onRefreshRequested()
{
    // 在这里实现你的数据刷新逻辑
    // 比如网络请求、数据库查询等
    
    // 模拟异步操作
    QTimer::singleShot(2000, this, &MyRefreshListWidget::simulateDataLoading);
}

void MyRefreshListWidget::simulateDataLoading()
{
    // 加载完成后添加新数据
    addItem("新的数据项");
    
    // 重要：完成刷新后必须调用此方法
    finishRefresh();
}
```

### 3. 可选配置

```cpp
// 设置刷新阈值（默认80像素）
setRefreshThreshold(100);

// 设置加载文本
setLoadingText("正在加载最新数据...");
```

## 工作原理

1. **检测下拉手势**：当用户在列表顶部向下拖拽时，组件会检测这个手势
2. **显示加载指示器**：达到一定拖拽距离时，显示加载动画和文本
3. **触发刷新**：当拖拽距离超过阈值并释放时，调用`onRefreshRequested()`虚函数
4. **完成刷新**：用户在刷新逻辑完成后调用`finishRefresh()`来隐藏加载指示器

## 注意事项

- 必须重写`onRefreshRequested()`虚函数
- 刷新完成后必须调用`finishRefresh()`
- 如果需要自定义加载动画，可以在`icons/`目录下放置`loading.gif`文件

## 文件结构

```
├── pulltorefreshlistwidget.h    # 头文件
├── pulltorefreshlistwidget.cpp  # 实现文件
├── resources.qrc                # 资源文件
├── icons/
│   └── loading.gif             # 加载动画（可选）
└── widget.cpp                  # 使用示例
```

## 编译说明

确保在.pro文件中包含了所有必要的文件：

```pro
SOURCES += pulltorefreshlistwidget.cpp
HEADERS += pulltorefreshlistwidget.h
RESOURCES += resources.qrc
```
