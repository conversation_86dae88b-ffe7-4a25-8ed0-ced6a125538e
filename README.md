# Qt Android 下拉刷新上拉加载示例

这个项目演示了如何在Qt Android应用中实现QListWidget的下拉刷新和上拉加载功能。

## 功能特性

### 🔄 下拉刷新
- 在列表顶部向下拖拽触发刷新
- 显示刷新动画和状态提示
- 支持触摸手势识别
- 自动隐藏刷新指示器

### ⬆️ 上拉加载更多
- 在列表底部向上拖拽触发加载
- 滚动到底部自动显示加载提示
- 支持无限滚动加载
- 加载完成后自动隐藏指示器

### 📱 Android优化
- 专为触摸界面设计
- 移除鼠标事件依赖
- 优化手势识别
- 适配Android平板系统

## 核心组件

### PullRefreshListWidget
自定义QListWidget类，继承并扩展了以下功能：

- **触摸事件处理**: 识别下拉和上拉手势
- **手势识别**: 支持QPanGesture平移手势
- **状态管理**: 管理刷新和加载状态
- **UI指示器**: 显示加载动画和文本提示

### 主要方法

```cpp
// 启用/禁用功能
void setRefreshEnabled(bool enabled);
void setLoadMoreEnabled(bool enabled);

// 完成操作
void finishRefresh();
void finishLoadMore();

// 信号
void pullToRefresh();
void pullToLoadMore();
```

## 使用方法

1. **创建列表控件**:
```cpp
PullRefreshListWidget *listWidget = new PullRefreshListWidget(this);
```

2. **连接信号**:
```cpp
connect(listWidget, &PullRefreshListWidget::pullToRefresh,
        this, &YourClass::onRefresh);
connect(listWidget, &PullRefreshListWidget::pullToLoadMore,
        this, &YourClass::onLoadMore);
```

3. **处理刷新和加载**:
```cpp
void YourClass::onRefresh() {
    // 执行刷新逻辑
    // ...
    listWidget->finishRefresh();
}

void YourClass::onLoadMore() {
    // 执行加载更多逻辑
    // ...
    listWidget->finishLoadMore();
}
```

## 配置参数

- `REFRESH_THRESHOLD = 80`: 下拉刷新触发阈值(像素)
- `LOAD_MORE_THRESHOLD = 50`: 上拉加载触发阈值(像素)

## 构建说明

1. 确保安装了Qt 5.9+和Android开发环境
2. 使用qmake构建项目:
```bash
qmake test.pro
make
```

## 测试功能

运行应用后可以测试以下操作：

1. **下拉刷新**: 在列表顶部向下拖拽，释放后触发刷新
2. **上拉加载**: 滚动到列表底部，向上拖拽触发加载更多
3. **自动加载**: 滚动接近底部时自动显示加载提示

## 注意事项

- 专为Android触摸设备优化
- 建议在真实设备上测试触摸体验
- 可根据需要调整触发阈值
- 支持自定义加载动画和样式
